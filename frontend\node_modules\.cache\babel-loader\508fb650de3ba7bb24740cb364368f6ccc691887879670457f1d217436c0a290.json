{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport Tabs from '@mui/material/Tabs';\nimport { useTabContext, getTabId, getPanelId } from '../TabContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst TabList = /*#__PURE__*/React.forwardRef(function TabList(props, ref) {\n  const {\n      children: childrenProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const context = useTabContext();\n  if (context === null) {\n    throw new TypeError('No TabContext provided');\n  }\n  const children = React.Children.map(childrenProp, child => {\n    if (! /*#__PURE__*/React.isValidElement(child)) {\n      return null;\n    }\n    return /*#__PURE__*/React.cloneElement(child, {\n      // SOMEDAY: `Tabs` will set those themselves\n      'aria-controls': getPanelId(context, child.props.value),\n      id: getTabId(context, child.props.value)\n    });\n  });\n  return /*#__PURE__*/_jsx(Tabs, _extends({}, other, {\n    ref: ref,\n    value: context.value,\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TabList.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A list of `<Tab />` elements.\n   */\n  children: PropTypes.node\n} : void 0;\nexport default TabList;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "Tabs", "useTabContext", "getTabId", "getPanelId", "jsx", "_jsx", "TabList", "forwardRef", "props", "ref", "children", "childrenProp", "other", "context", "TypeError", "Children", "map", "child", "isValidElement", "cloneElement", "value", "id", "process", "env", "NODE_ENV", "propTypes", "node"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/TabList/TabList.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport Tabs from '@mui/material/Tabs';\nimport { useTabContext, getTabId, getPanelId } from '../TabContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst TabList = /*#__PURE__*/React.forwardRef(function TabList(props, ref) {\n  const {\n      children: childrenProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const context = useTabContext();\n  if (context === null) {\n    throw new TypeError('No TabContext provided');\n  }\n  const children = React.Children.map(childrenProp, child => {\n    if (! /*#__PURE__*/React.isValidElement(child)) {\n      return null;\n    }\n    return /*#__PURE__*/React.cloneElement(child, {\n      // SOMEDAY: `Tabs` will set those themselves\n      'aria-controls': getPanelId(context, child.props.value),\n      id: getTabId(context, child.props.value)\n    });\n  });\n  return /*#__PURE__*/_jsx(Tabs, _extends({}, other, {\n    ref: ref,\n    value: context.value,\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TabList.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A list of `<Tab />` elements.\n   */\n  children: PropTypes.node\n} : void 0;\nexport default TabList;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,CAAC;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,oBAAoB;AACrC,SAASC,aAAa,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,eAAe;AACnE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,SAASD,OAAOA,CAACE,KAAK,EAAEC,GAAG,EAAE;EACzE,MAAM;MACFC,QAAQ,EAAEC;IACZ,CAAC,GAAGH,KAAK;IACTI,KAAK,GAAGhB,6BAA6B,CAACY,KAAK,EAAEX,SAAS,CAAC;EACzD,MAAMgB,OAAO,GAAGZ,aAAa,CAAC,CAAC;EAC/B,IAAIY,OAAO,KAAK,IAAI,EAAE;IACpB,MAAM,IAAIC,SAAS,CAAC,wBAAwB,CAAC;EAC/C;EACA,MAAMJ,QAAQ,GAAGZ,KAAK,CAACiB,QAAQ,CAACC,GAAG,CAACL,YAAY,EAAEM,KAAK,IAAI;IACzD,IAAI,EAAE,aAAanB,KAAK,CAACoB,cAAc,CAACD,KAAK,CAAC,EAAE;MAC9C,OAAO,IAAI;IACb;IACA,OAAO,aAAanB,KAAK,CAACqB,YAAY,CAACF,KAAK,EAAE;MAC5C;MACA,eAAe,EAAEd,UAAU,CAACU,OAAO,EAAEI,KAAK,CAACT,KAAK,CAACY,KAAK,CAAC;MACvDC,EAAE,EAAEnB,QAAQ,CAACW,OAAO,EAAEI,KAAK,CAACT,KAAK,CAACY,KAAK;IACzC,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO,aAAaf,IAAI,CAACL,IAAI,EAAEL,QAAQ,CAAC,CAAC,CAAC,EAAEiB,KAAK,EAAE;IACjDH,GAAG,EAAEA,GAAG;IACRW,KAAK,EAAEP,OAAO,CAACO,KAAK;IACpBV,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGlB,OAAO,CAACmB,SAAS,CAAC,yBAAyB;EACjF;EACA;EACA;EACA;EACA;AACF;AACA;EACEf,QAAQ,EAAEX,SAAS,CAAC2B;AACtB,CAAC,GAAG,KAAK,CAAC;AACV,eAAepB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}