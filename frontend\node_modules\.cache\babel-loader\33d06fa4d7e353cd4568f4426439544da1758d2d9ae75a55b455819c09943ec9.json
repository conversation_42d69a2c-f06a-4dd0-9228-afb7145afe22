{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport SpeedDialIcon from '@mui/material/SpeedDialIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\n\n/**\n * @ignore - do not document.\n */\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedSpeedDialIcon(props, ref) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The SpeedDialIcon component was moved from the lab to the core.', '', \"You should use `import { SpeedDialIcon } from '@mui/material'`\", \"or `import SpeedDialIcon from '@mui/material/SpeedDialIcon'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return /*#__PURE__*/_jsx(SpeedDialIcon, _extends({\n    ref: ref\n  }, props));\n});", "map": {"version": 3, "names": ["_extends", "React", "SpeedDialIcon", "jsx", "_jsx", "warnedOnce", "forwardRef", "DeprecatedSpeedDialIcon", "props", "ref", "console", "warn", "join"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/SpeedDialIcon/SpeedDialIcon.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport SpeedDialIcon from '@mui/material/SpeedDialIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\n\n/**\n * @ignore - do not document.\n */\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedSpeedDialIcon(props, ref) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The SpeedDialIcon component was moved from the lab to the core.', '', \"You should use `import { SpeedDialIcon } from '@mui/material'`\", \"or `import SpeedDialIcon from '@mui/material/SpeedDialIcon'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return /*#__PURE__*/_jsx(SpeedDialIcon, _extends({\n    ref: ref\n  }, props));\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,IAAIC,UAAU,GAAG,KAAK;;AAEtB;AACA;AACA;AACA,eAAe,aAAaJ,KAAK,CAACK,UAAU,CAAC,SAASC,uBAAuBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACxF,IAAI,CAACJ,UAAU,EAAE;IACfK,OAAO,CAACC,IAAI,CAAC,CAAC,sEAAsE,EAAE,EAAE,EAAE,gEAAgE,EAAE,8DAA8D,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvOP,UAAU,GAAG,IAAI;EACnB;EACA,OAAO,aAAaD,IAAI,CAACF,aAAa,EAAEF,QAAQ,CAAC;IAC/CS,GAAG,EAAEA;EACP,CAAC,EAAED,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}