-- Core tables for the materials management system

CREATE TABLE materials (
  id SERIAL PRIMARY KEY,
  sku VARCHAR(50) UNIQUE NOT NULL,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  unit_of_measure VARCHAR(20) NOT NULL,
  base_cost_per_unit DECIMAL(10,2) NOT NULL,
  category VARCHAR(50) NOT NULL
);

CREATE TABLE material_variants (
  id SERIAL PRIMARY KEY,
  material_id INTEGER REFERENCES materials(id),
  size_description VARCHAR(50),
  special_notes TEXT,
  price_adjustment DECIMAL(10,2) DEFAULT 0
);

CREATE TABLE customers (
  id SERIAL PRIMARY KEY,
  company_name VARCHAR(100) NOT NULL,
  contact_name VARCHAR(100),
  email VARCHAR(100),
  phone VARCHAR(20),
  credit_terms VARCHAR(20) DEFAULT 'COD',
  credit_limit DECIMAL(10,2)
);

CREATE TABLE orders (
  id SERIAL PRIMARY KEY,
  customer_id INTEGER REFERENCES customers(id),
  order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  status VARCHAR(20) DEFAULT 'Draft',
  subtotal DECIMAL(10,2),
  tax DECIMAL(10,2),
  delivery_fee DECIMAL(10,2),
  total DECIMAL(10,2)
);

CREATE TABLE order_items (
  id SERIAL PRIMARY KEY,
  order_id INTEGER REFERENCES orders(id),
  material_id INTEGER REFERENCES materials(id),
  variant_id INTEGER REFERENCES material_variants(id),
  quantity DECIMAL(10,2) NOT NULL,
  unit_price DECIMAL(10,2) NOT NULL,
  discount_percent DECIMAL(5,2) DEFAULT 0
);

CREATE TABLE deliveries (
  id SERIAL PRIMARY KEY,
  order_id INTEGER REFERENCES orders(id),
  pickup_location TEXT NOT NULL,
  dropoff_location TEXT NOT NULL,
  scheduled_date DATE,
  preferred_time_window VARCHAR(50),
  status VARCHAR(20) DEFAULT 'Scheduled',
  driver_id INTEGER,
  truck_id INTEGER,
  pod_image_url TEXT,
  pod_signature TEXT,
  pod_timestamp TIMESTAMP,
  pod_gps_coordinates VARCHAR(50)
);