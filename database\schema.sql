-- Core tables for the materials management system

-- Material categories and types
CREATE TABLE material_categories (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  icon VARCHAR(50)
);

CREATE TABLE unit_measures (
  id SERIAL PRIMARY KEY,
  name VA<PERSON>HAR(50) NOT NULL,
  abbreviation VA<PERSON>HAR(10) NOT NULL,
  type VARCHAR(20) NOT NULL -- 'weight', 'volume', 'area', 'length', 'count'
);

CREATE TABLE materials (
  id SERIAL PRIMARY KEY,
  sku VARCHAR(50) UNIQUE NOT NULL,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  category_id INTEGER REFERENCES material_categories(id),
  default_unit_measure_id INTEGER REFERENCES unit_measures(id),
  base_cost_per_unit DECIMAL(10,2) NOT NULL,
  allow_unit_override BOOLEAN DEFAULT true,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Material pricing by unit of measure
CREATE TABLE material_unit_pricing (
  id SERIAL PRIMARY KEY,
  material_id INTEGER REFERENCES materials(id),
  unit_measure_id INTEGER REFERENCES unit_measures(id),
  price_per_unit DECIMAL(10,2) NOT NULL,
  is_default BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE material_variants (
  id SERIAL PRIMARY KEY,
  material_id INTEGER REFERENCES materials(id),
  size_description VARCHAR(50),
  special_notes TEXT,
  price_adjustment DECIMAL(10,2) DEFAULT 0
);

CREATE TABLE customers (
  id SERIAL PRIMARY KEY,
  company_name VARCHAR(100) NOT NULL,
  contact_name VARCHAR(100),
  email VARCHAR(100),
  phone VARCHAR(20),
  credit_terms VARCHAR(20) DEFAULT 'COD',
  credit_limit DECIMAL(10,2)
);

-- Purchase Orders with auto-generated PO numbers
CREATE SEQUENCE po_number_seq START 1000;

CREATE TABLE purchase_orders (
  id SERIAL PRIMARY KEY,
  po_number VARCHAR(20) UNIQUE NOT NULL DEFAULT 'PO-' || LPAD(nextval('po_number_seq')::text, 6, '0'),
  customer_id INTEGER REFERENCES customers(id),
  order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  status VARCHAR(20) DEFAULT 'Draft',
  pickup_location TEXT,
  pickup_latitude DECIMAL(10,8),
  pickup_longitude DECIMAL(11,8),
  notes TEXT,
  subtotal DECIMAL(10,2),
  tax DECIMAL(10,2),
  delivery_fee DECIMAL(10,2),
  total DECIMAL(10,2),
  created_by VARCHAR(100),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Purchase Order Line Items
CREATE TABLE po_line_items (
  id SERIAL PRIMARY KEY,
  po_id INTEGER REFERENCES purchase_orders(id),
  material_id INTEGER REFERENCES materials(id),
  unit_measure_id INTEGER REFERENCES unit_measures(id),
  quantity DECIMAL(10,2) NOT NULL,
  unit_price DECIMAL(10,2) NOT NULL,
  hauling_rate DECIMAL(10,2) NOT NULL,
  dropoff_location TEXT NOT NULL,
  dropoff_latitude DECIMAL(10,8),
  dropoff_longitude DECIMAL(11,8),
  special_instructions TEXT,
  line_total DECIMAL(10,2) GENERATED ALWAYS AS (quantity * (unit_price + hauling_rate)) STORED,
  status VARCHAR(20) DEFAULT 'Pending',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE drivers (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  email VARCHAR(100) UNIQUE NOT NULL,
  phone VARCHAR(20),
  license_number VARCHAR(50),
  status VARCHAR(20) DEFAULT 'Active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE trucks (
  id SERIAL PRIMARY KEY,
  license_plate VARCHAR(20) UNIQUE NOT NULL,
  make VARCHAR(50),
  model VARCHAR(50),
  year INTEGER,
  capacity_tons DECIMAL(5,2),
  status VARCHAR(20) DEFAULT 'Available',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE deliveries (
  id SERIAL PRIMARY KEY,
  po_line_item_id INTEGER REFERENCES po_line_items(id),
  po_id INTEGER REFERENCES purchase_orders(id),
  pickup_location TEXT NOT NULL,
  dropoff_location TEXT NOT NULL,
  pickup_latitude DECIMAL(10,8),
  pickup_longitude DECIMAL(11,8),
  dropoff_latitude DECIMAL(10,8),
  dropoff_longitude DECIMAL(11,8),
  scheduled_date DATE,
  preferred_time_window VARCHAR(50),
  status VARCHAR(20) DEFAULT 'Scheduled',
  driver_id INTEGER REFERENCES drivers(id),
  truck_id INTEGER REFERENCES trucks(id),
  material_name TEXT,
  quantity DECIMAL(10,2),
  unit_measure TEXT,
  hauling_rate DECIMAL(10,2),
  special_instructions TEXT,
  pod_image_url TEXT,
  pod_signature TEXT,
  pod_timestamp TIMESTAMP,
  pod_gps_coordinates VARCHAR(50),
  current_latitude DECIMAL(10,8),
  current_longitude DECIMAL(11,8),
  last_location_update TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Geofencing tables
CREATE TABLE geofences (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  latitude DECIMAL(10,8) NOT NULL,
  longitude DECIMAL(11,8) NOT NULL,
  radius_meters INTEGER NOT NULL,
  type VARCHAR(50) NOT NULL, -- 'pickup', 'dropoff', 'yard', 'disposal'
  address TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE geofence_events (
  id SERIAL PRIMARY KEY,
  delivery_id INTEGER REFERENCES deliveries(id),
  geofence_id INTEGER REFERENCES geofences(id),
  driver_id INTEGER REFERENCES drivers(id),
  event_type VARCHAR(20) NOT NULL, -- 'enter', 'exit'
  latitude DECIMAL(10,8) NOT NULL,
  longitude DECIMAL(11,8) NOT NULL,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  auto_status_update VARCHAR(50), -- Status that was automatically set
  dwell_time_minutes INTEGER -- Time spent in geofence (for exit events)
);

-- Location tracking for real-time updates
CREATE TABLE location_updates (
  id SERIAL PRIMARY KEY,
  delivery_id INTEGER REFERENCES deliveries(id),
  driver_id INTEGER REFERENCES drivers(id),
  latitude DECIMAL(10,8) NOT NULL,
  longitude DECIMAL(11,8) NOT NULL,
  accuracy_meters DECIMAL(6,2),
  speed_kmh DECIMAL(5,2),
  heading_degrees INTEGER,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);