{"ast": null, "code": "import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nconst COMPONENT_NAME = 'Select';\nexport function getSelectUtilityClass(slot) {\n  return generateUtilityClass(COMPONENT_NAME, slot);\n}\nexport const selectClasses = generateUtilityClasses(COMPONENT_NAME, ['root', 'button', 'listbox', 'popup', 'active', 'expanded', 'disabled', 'focusVisible']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "COMPONENT_NAME", "getSelectUtilityClass", "slot", "selectClasses"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/base/Select/selectClasses.js"], "sourcesContent": ["import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nconst COMPONENT_NAME = 'Select';\nexport function getSelectUtilityClass(slot) {\n  return generateUtilityClass(COMPONENT_NAME, slot);\n}\nexport const selectClasses = generateUtilityClasses(COMPONENT_NAME, ['root', 'button', 'listbox', 'popup', 'active', 'expanded', 'disabled', 'focusVisible']);"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,MAAMC,cAAc,GAAG,QAAQ;AAC/B,OAAO,SAASC,qBAAqBA,CAACC,IAAI,EAAE;EAC1C,OAAOJ,oBAAoB,CAACE,cAAc,EAAEE,IAAI,CAAC;AACnD;AACA,OAAO,MAAMC,aAAa,GAAGJ,sBAAsB,CAACC,cAAc,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}