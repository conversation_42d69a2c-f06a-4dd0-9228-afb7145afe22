import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';

// Theme and Context
import modernTheme from './theme/modernTheme';
import { LanguageProvider } from './contexts/LanguageContext';

// Auth Components
import ModernLogin from './components/auth/ModernLogin';

// Admin Components
import DispatchDashboard from './components/DispatchDashboard';
import PurchaseOrderForm from './components/PurchaseOrderForm';
import GeofenceManager from './components/GeofenceManager';

// Driver Components
import DriverDashboard from './components/driver/DriverDashboard';
import DeliveryDetails from './components/driver/DeliveryDetails';

// Layout Components
import AdminLayout from './components/layout/AdminLayout';
import DriverLayout from './components/driver/DriverLayout';

function App() {
  return (
    <LanguageProvider>
      <ThemeProvider theme={modernTheme}>
        <CssBaseline />
        <Router>
          <Routes>
            {/* Auth Routes */}
            <Route path="/login" element={<ModernLogin />} />

            {/* Driver Routes */}
            <Route path="/driver" element={<DriverLayout />}>
              <Route index element={<Navigate to="/driver/dashboard" replace />} />
              <Route path="dashboard" element={<DriverDashboard />} />
              <Route path="delivery/:id" element={<DeliveryDetails />} />
            </Route>

            {/* Trucker Routes */}
            <Route path="/trucker" element={<DriverLayout />}>
              <Route index element={<Navigate to="/trucker/dashboard" replace />} />
              <Route path="dashboard" element={<DriverDashboard />} />
            </Route>

            {/* Admin Routes */}
            <Route path="/" element={<AdminLayout />}>
              <Route index element={<Navigate to="/dispatch" replace />} />
              <Route path="dispatch" element={<DispatchDashboard />} />
              <Route path="purchase-orders" element={<PurchaseOrderForm />} />
              <Route path="geofences" element={<GeofenceManager />} />
            </Route>

            {/* Redirect to login for unknown routes */}
            <Route path="*" element={<Navigate to="/login" replace />} />
          </Routes>
        </Router>
      </ThemeProvider>
    </LanguageProvider>
  );
}

export default App;
