import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';

// Admin Components
import DispatchDashboard from './components/DispatchDashboard';
import MaterialCatalog from './components/MaterialCatalog';
import OrderForm from './components/OrderForm';
import PurchaseOrderForm from './components/PurchaseOrderForm';
import GeofenceManager from './components/GeofenceManager';

// Driver Components
import DriverDashboard from './components/driver/DriverDashboard';
import DriverLogin from './components/driver/DriverLogin';
import DeliveryDetails from './components/driver/DeliveryDetails';

// Layout Components
import AdminLayout from './components/layout/AdminLayout';
import DriverLayout from './components/driver/DriverLayout';

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Routes>
          {/* Driver Routes */}
          <Route path="/driver/login" element={<DriverLogin />} />
          <Route path="/driver" element={<DriverLayout />}>
            <Route index element={<Navigate to="/driver/dashboard" replace />} />
            <Route path="dashboard" element={<DriverDashboard />} />
            <Route path="delivery/:id" element={<DeliveryDetails />} />
          </Route>

          {/* Admin Routes */}
          <Route path="/" element={<AdminLayout />}>
            <Route index element={<Navigate to="/dispatch" replace />} />
            <Route path="dispatch" element={<DispatchDashboard />} />
            <Route path="materials" element={<MaterialCatalog />} />
            <Route path="orders" element={<OrderForm />} />
            <Route path="purchase-orders" element={<PurchaseOrderForm />} />
            <Route path="geofences" element={<GeofenceManager />} />
          </Route>
        </Routes>
      </Router>
    </ThemeProvider>
  );
}

export default App;
