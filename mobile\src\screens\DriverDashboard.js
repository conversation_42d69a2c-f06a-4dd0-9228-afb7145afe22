import React, { useState, useEffect } from 'react';
import { View, Text, FlatList, StyleSheet, TouchableOpacity } from 'react-native';
import { Card, Button, Icon } from 'react-native-elements';
import axios from 'axios';

const DriverDashboard = ({ navigation }) => {
  const [deliveries, setDeliveries] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAssignedDeliveries();
  }, []);

  const fetchAssignedDeliveries = async () => {
    try {
      setLoading(true);
      const res = await axios.get('/api/deliveries/assigned');
      setDeliveries(res.data);
      setLoading(false);
    } catch (err) {
      console.error(err);
      setLoading(false);
    }
  };

  const updateDeliveryStatus = async (deliveryId, newStatus) => {
    try {
      await axios.put(`/api/deliveries/${deliveryId}/status`, { status: newStatus });
      fetchAssignedDeliveries();
    } catch (err) {
      console.error(err);
    }
  };

  const renderDeliveryCard = ({ item }) => (
    <Card containerStyle={styles.card}>
      <Card.Title>{item.material_name}</Card.Title>
      <Card.Divider />
      <View style={styles.detailRow}>
        <Icon name="location-pin" type="material" size={16} />
        <Text style={styles.detailText}>Pickup: {item.pickup_location}</Text>
      </View>
      <View style={styles.detailRow}>
        <Icon name="location-pin" type="material" size={16} />
        <Text style={styles.detailText}>Dropoff: {item.dropoff_location}</Text>
      </View>
      <View style={styles.detailRow}>
        <Icon name="schedule" type="material" size={16} />
        <Text style={styles.detailText}>
          Scheduled: {new Date(item.scheduled_date).toLocaleDateString()}
        </Text>
      </View>
      <View style={styles.detailRow}>
        <Icon name="info" type="material" size={16} />
        <Text style={styles.detailText}>Status: {item.status}</Text>
      </View>
      
      <View style={styles.buttonContainer}>
        {item.status === 'Scheduled' && (
          <Button
            title="Mark En Route"
            onPress={() => updateDeliveryStatus(item.id, 'En Route')}
            buttonStyle={styles.actionButton}
          />
        )}
        {item.status === 'En Route' && (
          <Button
            title="Mark Loaded"
            onPress={() => updateDeliveryStatus(item.id, 'Loaded')}
            buttonStyle={styles.actionButton}
          />
        )}
        {item.status === 'Loaded' && (
          <Button
            title="Mark In Transit"
            onPress={() => updateDeliveryStatus(item.id, 'In Transit')}
            buttonStyle={styles.actionButton}
          />
        )}
        {item.status === 'In Transit' && (
          <Button
            title="Mark Delivered"
            onPress={() => navigation.navigate('DeliveryConfirmation', { deliveryId: item.id })}
            buttonStyle={styles.actionButton}
          />
        )}
      </View>
    </Card>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.header}>My Deliveries</Text>
      <FlatList
        data={deliveries}
        renderItem={renderDeliveryCard}
        keyExtractor={item => item.id.toString()}
        refreshing={loading}
        onRefresh={fetchAssignedDeliveries}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 10,
    backgroundColor: '#f5f5f5'
  },
  header: {
    fontSize: 24,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center'
  },
  card: {
    borderRadius: 10,
    marginBottom: 15
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8
  },
  detailText: {
    marginLeft: 8,
    fontSize: 16
  },
  buttonContainer: {
    marginTop: 15
  },
  actionButton: {
    backgroundColor: '#2196F3',
    borderRadius: 5
  }
});

export default DriverDashboard;