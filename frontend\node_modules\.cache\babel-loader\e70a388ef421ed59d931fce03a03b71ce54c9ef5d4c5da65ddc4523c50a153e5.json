{"ast": null, "code": "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getTabPanelUtilityClass(slot) {\n  return generateUtilityClass('MuiTabPanel', slot);\n}\nconst tabPanelClasses = generateUtilityClasses('MuiTabPanel', ['root']);\nexport default tabPanelClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTabPanelUtilityClass", "slot", "tabPanelClasses"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/TabPanel/tabPanelClasses.js"], "sourcesContent": ["import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getTabPanelUtilityClass(slot) {\n  return generateUtilityClass('MuiTabPanel', slot);\n}\nconst tabPanelClasses = generateUtilityClasses('MuiTabPanel', ['root']);\nexport default tabPanelClasses;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,sBAAsB,MAAM,mCAAmC;AACtE,OAAO,SAASC,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,OAAOH,oBAAoB,CAAC,aAAa,EAAEG,IAAI,CAAC;AAClD;AACA,MAAMC,eAAe,GAAGH,sBAAsB,CAAC,aAAa,EAAE,CAAC,MAAM,CAAC,CAAC;AACvE,eAAeG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}