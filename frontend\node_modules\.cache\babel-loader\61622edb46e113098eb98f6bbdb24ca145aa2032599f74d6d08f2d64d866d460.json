{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport SpeedDialAction from '@mui/material/SpeedDialAction';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\n\n/**\n * @ignore - do not document.\n */\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedSpeedDialAction(props, ref) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The SpeedDialAction component was moved from the lab to the core.', '', \"You should use `import { SpeedDialAction } from '@mui/material'`\", \"or `import SpeedDialAction from '@mui/material/SpeedDialAction'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return /*#__PURE__*/_jsx(SpeedDialAction, _extends({\n    ref: ref\n  }, props));\n});", "map": {"version": 3, "names": ["_extends", "React", "SpeedDialAction", "jsx", "_jsx", "warnedOnce", "forwardRef", "DeprecatedSpeedDialAction", "props", "ref", "console", "warn", "join"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/SpeedDialAction/SpeedDialAction.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport SpeedDialAction from '@mui/material/SpeedDialAction';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\n\n/**\n * @ignore - do not document.\n */\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedSpeedDialAction(props, ref) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The SpeedDialAction component was moved from the lab to the core.', '', \"You should use `import { SpeedDialAction } from '@mui/material'`\", \"or `import SpeedDialAction from '@mui/material/SpeedDialAction'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return /*#__PURE__*/_jsx(SpeedDialAction, _extends({\n    ref: ref\n  }, props));\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,IAAIC,UAAU,GAAG,KAAK;;AAEtB;AACA;AACA;AACA,eAAe,aAAaJ,KAAK,CAACK,UAAU,CAAC,SAASC,yBAAyBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC1F,IAAI,CAACJ,UAAU,EAAE;IACfK,OAAO,CAACC,IAAI,CAAC,CAAC,wEAAwE,EAAE,EAAE,EAAE,kEAAkE,EAAE,kEAAkE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/OP,UAAU,GAAG,IAAI;EACnB;EACA,OAAO,aAAaD,IAAI,CAACF,eAAe,EAAEF,QAAQ,CAAC;IACjDS,GAAG,EAAEA;EACP,CAAC,EAAED,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}