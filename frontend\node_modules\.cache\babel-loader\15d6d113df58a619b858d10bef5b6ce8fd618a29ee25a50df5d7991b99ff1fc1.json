{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"className\", \"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { getTabPanelUtilityClass } from './tabPanelClasses';\nimport { getPanelId, getTabId, useTabContext } from '../TabContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getTabPanelUtilityClass, classes);\n};\nconst TabPanelRoot = styled('div', {\n  name: 'MuiTabPanel',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    padding: theme.spacing(3)\n  };\n});\nconst TabPanel = /*#__PURE__*/React.forwardRef(function TabPanel(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTabPanel'\n  });\n  const {\n      children,\n      className,\n      value\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props);\n  const classes = useUtilityClasses(ownerState);\n  const context = useTabContext();\n  if (context === null) {\n    throw new TypeError('No TabContext provided');\n  }\n  const id = getPanelId(context, value);\n  const tabId = getTabId(context, value);\n  return /*#__PURE__*/_jsx(TabPanelRoot, _extends({\n    \"aria-labelledby\": tabId,\n    className: clsx(classes.root, className),\n    hidden: value !== context.value,\n    id: id,\n    ref: ref,\n    role: \"tabpanel\",\n    ownerState: ownerState\n  }, other, {\n    children: value === context.value && children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TabPanel.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The `value` of the corresponding `Tab`. Must use the index of the `Tab` when\n   * no `value` was passed to `Tab`.\n   */\n  value: PropTypes.string.isRequired\n} : void 0;\nexport default TabPanel;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "clsx", "styled", "useThemeProps", "unstable_composeClasses", "composeClasses", "getTabPanelUtilityClass", "getPanelId", "getTabId", "useTabContext", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "TabPanelRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "theme", "padding", "spacing", "TabPanel", "forwardRef", "inProps", "ref", "children", "className", "value", "other", "context", "TypeError", "id", "tabId", "hidden", "role", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "sx", "oneOfType", "arrayOf", "func", "bool", "isRequired"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/TabPanel/TabPanel.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"className\", \"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { getTabPanelUtilityClass } from './tabPanelClasses';\nimport { getPanelId, getTabId, useTabContext } from '../TabContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getTabPanelUtilityClass, classes);\n};\nconst TabPanelRoot = styled('div', {\n  name: 'MuiTabPanel',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme\n}) => ({\n  padding: theme.spacing(3)\n}));\nconst TabPanel = /*#__PURE__*/React.forwardRef(function TabPanel(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTabPanel'\n  });\n  const {\n      children,\n      className,\n      value\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props);\n  const classes = useUtilityClasses(ownerState);\n  const context = useTabContext();\n  if (context === null) {\n    throw new TypeError('No TabContext provided');\n  }\n  const id = getPanelId(context, value);\n  const tabId = getTabId(context, value);\n  return /*#__PURE__*/_jsx(TabPanelRoot, _extends({\n    \"aria-labelledby\": tabId,\n    className: clsx(classes.root, className),\n    hidden: value !== context.value,\n    id: id,\n    ref: ref,\n    role: \"tabpanel\",\n    ownerState: ownerState\n  }, other, {\n    children: value === context.value && children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TabPanel.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The `value` of the corresponding `Tab`. Must use the index of the `Tab` when\n   * no `value` was passed to `Tab`.\n   */\n  value: PropTypes.string.isRequired\n} : void 0;\nexport default TabPanel;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC;AACpD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,WAAW;AACrE,SAASC,uBAAuB,QAAQ,mBAAmB;AAC3D,SAASC,UAAU,EAAEC,QAAQ,EAAEC,aAAa,QAAQ,eAAe;AACnE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOX,cAAc,CAACU,KAAK,EAAET,uBAAuB,EAAEQ,OAAO,CAAC;AAChE,CAAC;AACD,MAAMG,YAAY,GAAGf,MAAM,CAAC,KAAK,EAAE;EACjCgB,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAACO,IAAA;EAAA,IAAC;IACFC;EACF,CAAC,GAAAD,IAAA;EAAA,OAAM;IACLE,OAAO,EAAED,KAAK,CAACE,OAAO,CAAC,CAAC;EAC1B,CAAC;AAAA,CAAC,CAAC;AACH,MAAMC,QAAQ,GAAG,aAAa5B,KAAK,CAAC6B,UAAU,CAAC,SAASD,QAAQA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC7E,MAAMT,KAAK,GAAGlB,aAAa,CAAC;IAC1BkB,KAAK,EAAEQ,OAAO;IACdX,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFa,QAAQ;MACRC,SAAS;MACTC;IACF,CAAC,GAAGZ,KAAK;IACTa,KAAK,GAAGrC,6BAA6B,CAACwB,KAAK,EAAEvB,SAAS,CAAC;EACzD,MAAMe,UAAU,GAAGjB,QAAQ,CAAC,CAAC,CAAC,EAAEyB,KAAK,CAAC;EACtC,MAAMP,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMsB,OAAO,GAAG1B,aAAa,CAAC,CAAC;EAC/B,IAAI0B,OAAO,KAAK,IAAI,EAAE;IACpB,MAAM,IAAIC,SAAS,CAAC,wBAAwB,CAAC;EAC/C;EACA,MAAMC,EAAE,GAAG9B,UAAU,CAAC4B,OAAO,EAAEF,KAAK,CAAC;EACrC,MAAMK,KAAK,GAAG9B,QAAQ,CAAC2B,OAAO,EAAEF,KAAK,CAAC;EACtC,OAAO,aAAatB,IAAI,CAACM,YAAY,EAAErB,QAAQ,CAAC;IAC9C,iBAAiB,EAAE0C,KAAK;IACxBN,SAAS,EAAE/B,IAAI,CAACa,OAAO,CAACE,IAAI,EAAEgB,SAAS,CAAC;IACxCO,MAAM,EAAEN,KAAK,KAAKE,OAAO,CAACF,KAAK;IAC/BI,EAAE,EAAEA,EAAE;IACNP,GAAG,EAAEA,GAAG;IACRU,IAAI,EAAE,UAAU;IAChB3B,UAAU,EAAEA;EACd,CAAC,EAAEqB,KAAK,EAAE;IACRH,QAAQ,EAAEE,KAAK,KAAKE,OAAO,CAACF,KAAK,IAAIF;EACvC,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGhB,QAAQ,CAACiB,SAAS,CAAC,yBAAyB;EAClF;EACA;EACA;EACA;EACA;AACF;AACA;EACEb,QAAQ,EAAE/B,SAAS,CAAC6C,IAAI;EACxB;AACF;AACA;EACE/B,OAAO,EAAEd,SAAS,CAAC8C,MAAM;EACzB;AACF;AACA;EACEd,SAAS,EAAEhC,SAAS,CAAC+C,MAAM;EAC3B;AACF;AACA;EACEC,EAAE,EAAEhD,SAAS,CAACiD,SAAS,CAAC,CAACjD,SAAS,CAACkD,OAAO,CAAClD,SAAS,CAACiD,SAAS,CAAC,CAACjD,SAAS,CAACmD,IAAI,EAAEnD,SAAS,CAAC8C,MAAM,EAAE9C,SAAS,CAACoD,IAAI,CAAC,CAAC,CAAC,EAAEpD,SAAS,CAACmD,IAAI,EAAEnD,SAAS,CAAC8C,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACEb,KAAK,EAAEjC,SAAS,CAAC+C,MAAM,CAACM;AAC1B,CAAC,GAAG,KAAK,CAAC;AACV,eAAe1B,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}