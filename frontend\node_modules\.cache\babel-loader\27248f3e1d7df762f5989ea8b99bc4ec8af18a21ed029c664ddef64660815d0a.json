{"ast": null, "code": "import React from'react';import{BrowserRouter as Router,Routes,Route,Navigate}from'react-router-dom';import{ThemeProvider,createTheme}from'@mui/material/styles';import CssBaseline from'@mui/material/CssBaseline';// Admin Components\nimport DispatchDashboard from'./components/DispatchDashboard';import MaterialCatalog from'./components/MaterialCatalog';import OrderForm from'./components/OrderForm';import PurchaseOrderForm from'./components/PurchaseOrderForm';import GeofenceManager from'./components/GeofenceManager';// Driver Components\nimport DriverDashboard from'./components/driver/DriverDashboard';import DriverLogin from'./components/driver/DriverLogin';import DeliveryDetails from'./components/driver/DeliveryDetails';// Layout Components\nimport AdminLayout from'./components/layout/AdminLayout';import DriverLayout from'./components/driver/DriverLayout';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const theme=createTheme({palette:{primary:{main:'#1976d2'},secondary:{main:'#dc004e'}}});function App(){return/*#__PURE__*/_jsxs(ThemeProvider,{theme:theme,children:[/*#__PURE__*/_jsx(CssBaseline,{}),/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/driver/login\",element:/*#__PURE__*/_jsx(DriverLogin,{})}),/*#__PURE__*/_jsxs(Route,{path:\"/driver\",element:/*#__PURE__*/_jsx(DriverLayout,{}),children:[/*#__PURE__*/_jsx(Route,{index:true,element:/*#__PURE__*/_jsx(Navigate,{to:\"/driver/dashboard\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"dashboard\",element:/*#__PURE__*/_jsx(DriverDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"delivery/:id\",element:/*#__PURE__*/_jsx(DeliveryDetails,{})})]}),/*#__PURE__*/_jsxs(Route,{path:\"/\",element:/*#__PURE__*/_jsx(AdminLayout,{}),children:[/*#__PURE__*/_jsx(Route,{index:true,element:/*#__PURE__*/_jsx(Navigate,{to:\"/dispatch\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"dispatch\",element:/*#__PURE__*/_jsx(DispatchDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"materials\",element:/*#__PURE__*/_jsx(MaterialCatalog,{})}),/*#__PURE__*/_jsx(Route,{path:\"orders\",element:/*#__PURE__*/_jsx(OrderForm,{})}),/*#__PURE__*/_jsx(Route,{path:\"purchase-orders\",element:/*#__PURE__*/_jsx(PurchaseOrderForm,{})}),/*#__PURE__*/_jsx(Route,{path:\"geofences\",element:/*#__PURE__*/_jsx(GeofenceManager,{})})]})]})})]});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "ThemeProvider", "createTheme", "CssBaseline", "DispatchDashboard", "MaterialCatalog", "OrderForm", "PurchaseOrderForm", "GeofenceManager", "DriverDashboard", "<PERSON><PERSON><PERSON>in", "DeliveryDetails", "AdminLayout", "DriverLayout", "jsx", "_jsx", "jsxs", "_jsxs", "theme", "palette", "primary", "main", "secondary", "App", "children", "path", "element", "index", "to", "replace"], "sources": ["C:/NewSiteKevin/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\n\n// Admin Components\nimport DispatchDashboard from './components/DispatchDashboard';\nimport MaterialCatalog from './components/MaterialCatalog';\nimport OrderForm from './components/OrderForm';\nimport PurchaseOrderForm from './components/PurchaseOrderForm';\nimport GeofenceManager from './components/GeofenceManager';\n\n// Driver Components\nimport DriverDashboard from './components/driver/DriverDashboard';\nimport DriverLogin from './components/driver/DriverLogin';\nimport DeliveryDetails from './components/driver/DeliveryDetails';\n\n// Layout Components\nimport AdminLayout from './components/layout/AdminLayout';\nimport DriverLayout from './components/driver/DriverLayout';\n\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#1976d2',\n    },\n    secondary: {\n      main: '#dc004e',\n    },\n  },\n});\n\nfunction App() {\n  return (\n    <ThemeProvider theme={theme}>\n      <CssBaseline />\n      <Router>\n        <Routes>\n          {/* Driver Routes */}\n          <Route path=\"/driver/login\" element={<DriverLogin />} />\n          <Route path=\"/driver\" element={<DriverLayout />}>\n            <Route index element={<Navigate to=\"/driver/dashboard\" replace />} />\n            <Route path=\"dashboard\" element={<DriverDashboard />} />\n            <Route path=\"delivery/:id\" element={<DeliveryDetails />} />\n          </Route>\n\n          {/* Admin Routes */}\n          <Route path=\"/\" element={<AdminLayout />}>\n            <Route index element={<Navigate to=\"/dispatch\" replace />} />\n            <Route path=\"dispatch\" element={<DispatchDashboard />} />\n            <Route path=\"materials\" element={<MaterialCatalog />} />\n            <Route path=\"orders\" element={<OrderForm />} />\n            <Route path=\"purchase-orders\" element={<PurchaseOrderForm />} />\n            <Route path=\"geofences\" element={<GeofenceManager />} />\n          </Route>\n        </Routes>\n      </Router>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,KAAQ,kBAAkB,CACnF,OAASC,aAAa,CAAEC,WAAW,KAAQ,sBAAsB,CACjE,MAAO,CAAAC,WAAW,KAAM,2BAA2B,CAEnD;AACA,MAAO,CAAAC,iBAAiB,KAAM,gCAAgC,CAC9D,MAAO,CAAAC,eAAe,KAAM,8BAA8B,CAC1D,MAAO,CAAAC,SAAS,KAAM,wBAAwB,CAC9C,MAAO,CAAAC,iBAAiB,KAAM,gCAAgC,CAC9D,MAAO,CAAAC,eAAe,KAAM,8BAA8B,CAE1D;AACA,MAAO,CAAAC,eAAe,KAAM,qCAAqC,CACjE,MAAO,CAAAC,WAAW,KAAM,iCAAiC,CACzD,MAAO,CAAAC,eAAe,KAAM,qCAAqC,CAEjE;AACA,MAAO,CAAAC,WAAW,KAAM,iCAAiC,CACzD,MAAO,CAAAC,YAAY,KAAM,kCAAkC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5D,KAAM,CAAAC,KAAK,CAAGhB,WAAW,CAAC,CACxBiB,OAAO,CAAE,CACPC,OAAO,CAAE,CACPC,IAAI,CAAE,SACR,CAAC,CACDC,SAAS,CAAE,CACTD,IAAI,CAAE,SACR,CACF,CACF,CAAC,CAAC,CAEF,QAAS,CAAAE,GAAGA,CAAA,CAAG,CACb,mBACEN,KAAA,CAAChB,aAAa,EAACiB,KAAK,CAAEA,KAAM,CAAAM,QAAA,eAC1BT,IAAA,CAACZ,WAAW,GAAE,CAAC,cACfY,IAAA,CAAClB,MAAM,EAAA2B,QAAA,cACLP,KAAA,CAACnB,MAAM,EAAA0B,QAAA,eAELT,IAAA,CAAChB,KAAK,EAAC0B,IAAI,CAAC,eAAe,CAACC,OAAO,cAAEX,IAAA,CAACL,WAAW,GAAE,CAAE,CAAE,CAAC,cACxDO,KAAA,CAAClB,KAAK,EAAC0B,IAAI,CAAC,SAAS,CAACC,OAAO,cAAEX,IAAA,CAACF,YAAY,GAAE,CAAE,CAAAW,QAAA,eAC9CT,IAAA,CAAChB,KAAK,EAAC4B,KAAK,MAACD,OAAO,cAAEX,IAAA,CAACf,QAAQ,EAAC4B,EAAE,CAAC,mBAAmB,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,cACrEd,IAAA,CAAChB,KAAK,EAAC0B,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEX,IAAA,CAACN,eAAe,GAAE,CAAE,CAAE,CAAC,cACxDM,IAAA,CAAChB,KAAK,EAAC0B,IAAI,CAAC,cAAc,CAACC,OAAO,cAAEX,IAAA,CAACJ,eAAe,GAAE,CAAE,CAAE,CAAC,EACtD,CAAC,cAGRM,KAAA,CAAClB,KAAK,EAAC0B,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEX,IAAA,CAACH,WAAW,GAAE,CAAE,CAAAY,QAAA,eACvCT,IAAA,CAAChB,KAAK,EAAC4B,KAAK,MAACD,OAAO,cAAEX,IAAA,CAACf,QAAQ,EAAC4B,EAAE,CAAC,WAAW,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,cAC7Dd,IAAA,CAAChB,KAAK,EAAC0B,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEX,IAAA,CAACX,iBAAiB,GAAE,CAAE,CAAE,CAAC,cACzDW,IAAA,CAAChB,KAAK,EAAC0B,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEX,IAAA,CAACV,eAAe,GAAE,CAAE,CAAE,CAAC,cACxDU,IAAA,CAAChB,KAAK,EAAC0B,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEX,IAAA,CAACT,SAAS,GAAE,CAAE,CAAE,CAAC,cAC/CS,IAAA,CAAChB,KAAK,EAAC0B,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEX,IAAA,CAACR,iBAAiB,GAAE,CAAE,CAAE,CAAC,cAChEQ,IAAA,CAAChB,KAAK,EAAC0B,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEX,IAAA,CAACP,eAAe,GAAE,CAAE,CAAE,CAAC,EACnD,CAAC,EACF,CAAC,CACH,CAAC,EACI,CAAC,CAEpB,CAEA,cAAe,CAAAe,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}