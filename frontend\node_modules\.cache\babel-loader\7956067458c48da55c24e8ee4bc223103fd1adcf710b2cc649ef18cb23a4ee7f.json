{"ast": null, "code": "export * from './useTransitionStateManager';\nexport * from './useTransitionTrigger';\nexport * from './TransitionContext';", "map": {"version": 3, "names": [], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/base/useTransition/index.js"], "sourcesContent": ["export * from './useTransitionStateManager';\nexport * from './useTransitionTrigger';\nexport * from './TransitionContext';"], "mappings": "AAAA,cAAc,6BAA6B;AAC3C,cAAc,wBAAwB;AACtC,cAAc,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}