{"ast": null, "code": "export { default as resolveComponentProps } from '@mui/utils/resolveComponentProps';", "map": {"version": 3, "names": ["default", "resolveComponentProps"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/base/utils/resolveComponentProps.js"], "sourcesContent": ["export { default as resolveComponentProps } from '@mui/utils/resolveComponentProps';"], "mappings": "AAAA,SAASA,OAAO,IAAIC,qBAAqB,QAAQ,kCAAkC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}