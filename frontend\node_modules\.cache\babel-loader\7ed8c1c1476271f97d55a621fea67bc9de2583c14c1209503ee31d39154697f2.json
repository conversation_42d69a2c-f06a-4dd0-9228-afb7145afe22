{"ast": null, "code": "import { generateUtilityClasses } from '../generateUtilityClasses';\nimport { generateUtilityClass } from '../generateUtilityClass';\nconst COMPONENT_NAME = 'Modal';\nexport function getModalUtilityClass(slot) {\n  return generateUtilityClass(COMPONENT_NAME, slot);\n}\nexport const modalClasses = generateUtilityClasses(COMPONENT_NAME, ['root', 'hidden', 'backdrop']);", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "COMPONENT_NAME", "getModalUtilityClass", "slot", "modalClasses"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/base/Modal/modalClasses.js"], "sourcesContent": ["import { generateUtilityClasses } from '../generateUtilityClasses';\nimport { generateUtilityClass } from '../generateUtilityClass';\nconst COMPONENT_NAME = 'Modal';\nexport function getModalUtilityClass(slot) {\n  return generateUtilityClass(COMPONENT_NAME, slot);\n}\nexport const modalClasses = generateUtilityClasses(COMPONENT_NAME, ['root', 'hidden', 'backdrop']);"], "mappings": "AAAA,SAASA,sBAAsB,QAAQ,2BAA2B;AAClE,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,MAAMC,cAAc,GAAG,OAAO;AAC9B,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EACzC,OAAOH,oBAAoB,CAACC,cAAc,EAAEE,IAAI,CAAC;AACnD;AACA,OAAO,MAAMC,YAAY,GAAGL,sBAAsB,CAACE,cAAc,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}