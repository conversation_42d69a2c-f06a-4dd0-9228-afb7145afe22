{"ast": null, "code": "/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The DateRangePicker component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`', '', \"You should use `import { DateRangePicker } from '@mui/x-date-pickers-pro'`\", \"or `import { DateRangePicker } from '@mui/x-date-pickers-pro/DateRangePicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The DateRangePicker component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst DateRangePicker = /*#__PURE__*/React.forwardRef(function DeprecatedDateRangePicker() {\n  warn();\n  return null;\n});\nexport default DateRangePicker;", "map": {"version": 3, "names": ["React", "warnedOnce", "warn", "console", "join", "DateRangePicker", "forwardRef", "DeprecatedDateRangePicker"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/DateRangePicker/DateRangePicker.js"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The DateRangePicker component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`', '', \"You should use `import { DateRangePicker } from '@mui/x-date-pickers-pro'`\", \"or `import { DateRangePicker } from '@mui/x-date-pickers-pro/DateRangePicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The DateRangePicker component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst DateRangePicker = /*#__PURE__*/React.forwardRef(function DeprecatedDateRangePicker() {\n  warn();\n  return null;\n});\nexport default DateRangePicker;"], "mappings": "AAAA;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,UAAU,GAAG,KAAK;AACtB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,IAAI,CAACD,UAAU,EAAE;IACfE,OAAO,CAACD,IAAI,CAAC,CAAC,2FAA2F,EAAE,EAAE,EAAE,4EAA4E,EAAE,gFAAgF,EAAE,EAAE,EAAE,qGAAqG,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;IACrYH,UAAU,GAAG,IAAI;EACnB;AACF,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMI,eAAe,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,SAASC,yBAAyBA,CAAA,EAAG;EACzFL,IAAI,CAAC,CAAC;EACN,OAAO,IAAI;AACb,CAAC,CAAC;AACF,eAAeG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}