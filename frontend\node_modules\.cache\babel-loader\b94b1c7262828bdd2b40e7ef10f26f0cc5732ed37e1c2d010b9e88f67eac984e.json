{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport Typography from '@mui/material/Typography';\nimport TimelineContext from '../Timeline/TimelineContext';\nimport { getTimelineOppositeContentUtilityClass } from './timelineOppositeContentClasses';\nimport convertTimelinePositionToClass from '../internal/convertTimelinePositionToClass';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    position,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', convertTimelinePositionToClass(position)]\n  };\n  return composeClasses(slots, getTimelineOppositeContentUtilityClass, classes);\n};\nconst TimelineOppositeContentRoot = styled(Typography, {\n  name: 'MuiTimelineOppositeContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[convertTimelinePositionToClass(ownerState.position)]];\n  }\n})(_ref => {\n  let {\n    ownerState\n  } = _ref;\n  return _extends({\n    padding: '6px 16px',\n    marginRight: 'auto',\n    textAlign: 'right',\n    flex: 1\n  }, ownerState.position === 'left' && {\n    textAlign: 'left'\n  });\n});\nconst TimelineOppositeContent = /*#__PURE__*/React.forwardRef(function TimelineOppositeContent(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimelineOppositeContent'\n  });\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    position: positionContext\n  } = React.useContext(TimelineContext);\n  const ownerState = _extends({}, props, {\n    position: positionContext || 'left'\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(TimelineOppositeContentRoot, _extends({\n    component: \"div\",\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? TimelineOppositeContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nTimelineOppositeContent.muiName = 'TimelineOppositeContent';\nexport default TimelineOppositeContent;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "styled", "useThemeProps", "unstable_composeClasses", "composeClasses", "Typography", "TimelineContext", "getTimelineOppositeContentUtilityClass", "convertTimelinePositionToClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "position", "classes", "slots", "root", "TimelineOppositeContentRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "padding", "marginRight", "textAlign", "flex", "TimelineOppositeContent", "forwardRef", "inProps", "ref", "className", "other", "positionContext", "useContext", "component", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "sx", "oneOfType", "arrayOf", "func", "bool", "mui<PERSON><PERSON>"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/TimelineOppositeContent/TimelineOppositeContent.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport Typography from '@mui/material/Typography';\nimport TimelineContext from '../Timeline/TimelineContext';\nimport { getTimelineOppositeContentUtilityClass } from './timelineOppositeContentClasses';\nimport convertTimelinePositionToClass from '../internal/convertTimelinePositionToClass';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    position,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', convertTimelinePositionToClass(position)]\n  };\n  return composeClasses(slots, getTimelineOppositeContentUtilityClass, classes);\n};\nconst TimelineOppositeContentRoot = styled(Typography, {\n  name: 'MuiTimelineOppositeContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[convertTimelinePositionToClass(ownerState.position)]];\n  }\n})(({\n  ownerState\n}) => _extends({\n  padding: '6px 16px',\n  marginRight: 'auto',\n  textAlign: 'right',\n  flex: 1\n}, ownerState.position === 'left' && {\n  textAlign: 'left'\n}));\nconst TimelineOppositeContent = /*#__PURE__*/React.forwardRef(function TimelineOppositeContent(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimelineOppositeContent'\n  });\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    position: positionContext\n  } = React.useContext(TimelineContext);\n  const ownerState = _extends({}, props, {\n    position: positionContext || 'left'\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(TimelineOppositeContentRoot, _extends({\n    component: \"div\",\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? TimelineOppositeContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nTimelineOppositeContent.muiName = 'TimelineOppositeContent';\nexport default TimelineOppositeContent;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,CAAC;AAC/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,WAAW;AACrE,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,eAAe,MAAM,6BAA6B;AACzD,SAASC,sCAAsC,QAAQ,kCAAkC;AACzF,OAAOC,8BAA8B,MAAM,4CAA4C;AACvF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,QAAQ;IACRC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAER,8BAA8B,CAACK,QAAQ,CAAC;EACzD,CAAC;EACD,OAAOT,cAAc,CAACW,KAAK,EAAER,sCAAsC,EAAEO,OAAO,CAAC;AAC/E,CAAC;AACD,MAAMG,2BAA2B,GAAGhB,MAAM,CAACI,UAAU,EAAE;EACrDa,IAAI,EAAE,4BAA4B;EAClCC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJV;IACF,CAAC,GAAGS,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEM,MAAM,CAACd,8BAA8B,CAACI,UAAU,CAACC,QAAQ,CAAC,CAAC,CAAC;EACnF;AACF,CAAC,CAAC,CAACU,IAAA;EAAA,IAAC;IACFX;EACF,CAAC,GAAAW,IAAA;EAAA,OAAK3B,QAAQ,CAAC;IACb4B,OAAO,EAAE,UAAU;IACnBC,WAAW,EAAE,MAAM;IACnBC,SAAS,EAAE,OAAO;IAClBC,IAAI,EAAE;EACR,CAAC,EAAEf,UAAU,CAACC,QAAQ,KAAK,MAAM,IAAI;IACnCa,SAAS,EAAE;EACb,CAAC,CAAC;AAAA,EAAC;AACH,MAAME,uBAAuB,GAAG,aAAa9B,KAAK,CAAC+B,UAAU,CAAC,SAASD,uBAAuBA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC3G,MAAMV,KAAK,GAAGnB,aAAa,CAAC;IAC1BmB,KAAK,EAAES,OAAO;IACdZ,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFc;IACF,CAAC,GAAGX,KAAK;IACTY,KAAK,GAAGtC,6BAA6B,CAAC0B,KAAK,EAAExB,SAAS,CAAC;EACzD,MAAM;IACJgB,QAAQ,EAAEqB;EACZ,CAAC,GAAGpC,KAAK,CAACqC,UAAU,CAAC7B,eAAe,CAAC;EACrC,MAAMM,UAAU,GAAGhB,QAAQ,CAAC,CAAC,CAAC,EAAEyB,KAAK,EAAE;IACrCR,QAAQ,EAAEqB,eAAe,IAAI;EAC/B,CAAC,CAAC;EACF,MAAMpB,OAAO,GAAGH,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACO,2BAA2B,EAAErB,QAAQ,CAAC;IAC7DwC,SAAS,EAAE,KAAK;IAChBJ,SAAS,EAAEhC,IAAI,CAACc,OAAO,CAACE,IAAI,EAAEgB,SAAS,CAAC;IACxCpB,UAAU,EAAEA,UAAU;IACtBmB,GAAG,EAAEA;EACP,CAAC,EAAEE,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACFI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGX,uBAAuB,CAACY,SAAS,CAAC,yBAAyB;EACjG;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAE1C,SAAS,CAAC2C,IAAI;EACxB;AACF;AACA;EACE5B,OAAO,EAAEf,SAAS,CAAC4C,MAAM;EACzB;AACF;AACA;EACEX,SAAS,EAAEjC,SAAS,CAAC6C,MAAM;EAC3B;AACF;AACA;EACEC,EAAE,EAAE9C,SAAS,CAAC+C,SAAS,CAAC,CAAC/C,SAAS,CAACgD,OAAO,CAAChD,SAAS,CAAC+C,SAAS,CAAC,CAAC/C,SAAS,CAACiD,IAAI,EAAEjD,SAAS,CAAC4C,MAAM,EAAE5C,SAAS,CAACkD,IAAI,CAAC,CAAC,CAAC,EAAElD,SAAS,CAACiD,IAAI,EAAEjD,SAAS,CAAC4C,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACVf,uBAAuB,CAACsB,OAAO,GAAG,yBAAyB;AAC3D,eAAetB,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}