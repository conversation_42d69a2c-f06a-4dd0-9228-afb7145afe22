{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The LocalizationProvider component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { LocalizationProvider } from '@mui/x-date-pickers'`\", \"or `import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The LocalizationProvider component was moved from `@mui/lab` to `@mui/x-date-pickers`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst LocalizationProvider = /*#__PURE__*/React.forwardRef(function DeprecatedLocalizationProvider() {\n  warn();\n  return null;\n});\nexport default LocalizationProvider;", "map": {"version": 3, "names": ["React", "warnedOnce", "warn", "console", "join", "LocalizationProvider", "forwardRef", "DeprecatedLocalizationProvider"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/LocalizationProvider/LocalizationProvider.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The LocalizationProvider component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { LocalizationProvider } from '@mui/x-date-pickers'`\", \"or `import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The LocalizationProvider component was moved from `@mui/lab` to `@mui/x-date-pickers`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst LocalizationProvider = /*#__PURE__*/React.forwardRef(function DeprecatedLocalizationProvider() {\n  warn();\n  return null;\n});\nexport default LocalizationProvider;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,UAAU,GAAG,KAAK;AACtB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,IAAI,CAACD,UAAU,EAAE;IACfE,OAAO,CAACD,IAAI,CAAC,CAAC,6FAA6F,EAAE,EAAE,EAAE,6EAA6E,EAAE,sFAAsF,EAAE,EAAE,EAAE,qGAAqG,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9YH,UAAU,GAAG,IAAI;EACnB;AACF,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMI,oBAAoB,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,SAASC,8BAA8BA,CAAA,EAAG;EACnGL,IAAI,CAAC,CAAC;EACN,OAAO,IAAI;AACb,CAAC,CAAC;AACF,eAAeG,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}