{"ast": null, "code": "'use client';\n\nexport { Portal } from './Portal';\nexport * from './Portal.types';", "map": {"version": 3, "names": ["Portal"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/base/Portal/index.js"], "sourcesContent": ["'use client';\n\nexport { Portal } from './Portal';\nexport * from './Portal.types';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,MAAM,QAAQ,UAAU;AACjC,cAAc,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}