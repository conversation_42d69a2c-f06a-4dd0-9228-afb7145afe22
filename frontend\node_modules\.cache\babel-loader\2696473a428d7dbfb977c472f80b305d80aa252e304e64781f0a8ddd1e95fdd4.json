{"ast": null, "code": "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getTimelineOppositeContentUtilityClass(slot) {\n  return generateUtilityClass('MuiTimelineOppositeContent', slot);\n}\nconst timelineOppositeContentClasses = generateUtilityClasses('MuiTimelineOppositeContent', ['root', 'positionLeft', 'positionRight', 'positionAlternate', 'positionAlternateReverse']);\nexport default timelineOppositeContentClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTimelineOppositeContentUtilityClass", "slot", "timelineOppositeContentClasses"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/TimelineOppositeContent/timelineOppositeContentClasses.js"], "sourcesContent": ["import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getTimelineOppositeContentUtilityClass(slot) {\n  return generateUtilityClass('MuiTimelineOppositeContent', slot);\n}\nconst timelineOppositeContentClasses = generateUtilityClasses('MuiTimelineOppositeContent', ['root', 'positionLeft', 'positionRight', 'positionAlternate', 'positionAlternateReverse']);\nexport default timelineOppositeContentClasses;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,sBAAsB,MAAM,mCAAmC;AACtE,OAAO,SAASC,sCAAsCA,CAACC,IAAI,EAAE;EAC3D,OAAOH,oBAAoB,CAAC,4BAA4B,EAAEG,IAAI,CAAC;AACjE;AACA,MAAMC,8BAA8B,GAAGH,sBAAsB,CAAC,4BAA4B,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,eAAe,EAAE,mBAAmB,EAAE,0BAA0B,CAAC,CAAC;AACvL,eAAeG,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}