{"ast": null, "code": "'use client';\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The DesktopDatePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { DesktopDatePicker } from '@mui/x-date-pickers'`\", \"or `import { DesktopDatePicker } from '@mui/x-date-pickers/DesktopDatePicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The DesktopDatePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst DesktopDatePicker = /*#__PURE__*/React.forwardRef(function DeprecatedDesktopDatePicker() {\n  warn();\n  return null;\n});\nexport default DesktopDatePicker;", "map": {"version": 3, "names": ["React", "warnedOnce", "warn", "console", "join", "DesktopDatePicker", "forwardRef", "DeprecatedDesktopDatePicker"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/DesktopDatePicker/DesktopDatePicker.js"], "sourcesContent": ["'use client';\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The DesktopDatePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { DesktopDatePicker } from '@mui/x-date-pickers'`\", \"or `import { DesktopDatePicker } from '@mui/x-date-pickers/DesktopDatePicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The DesktopDatePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst DesktopDatePicker = /*#__PURE__*/React.forwardRef(function DeprecatedDesktopDatePicker() {\n  warn();\n  return null;\n});\nexport default DesktopDatePicker;"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,UAAU,GAAG,KAAK;AACtB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,IAAI,CAACD,UAAU,EAAE;IACfE,OAAO,CAACD,IAAI,CAAC,CAAC,0FAA0F,EAAE,EAAE,EAAE,0EAA0E,EAAE,gFAAgF,EAAE,EAAE,EAAE,qGAAqG,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;IAClYH,UAAU,GAAG,IAAI;EACnB;AACF,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMI,iBAAiB,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,SAASC,2BAA2BA,CAAA,EAAG;EAC7FL,IAAI,CAAC,CAAC;EACN,OAAO,IAAI;AACb,CAAC,CAAC;AACF,eAAeG,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}