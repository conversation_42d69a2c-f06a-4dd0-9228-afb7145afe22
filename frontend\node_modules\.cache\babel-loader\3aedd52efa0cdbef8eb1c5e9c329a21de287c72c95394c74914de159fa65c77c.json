{"ast": null, "code": "export { unstable_composeClasses } from '@mui/utils';", "map": {"version": 3, "names": ["unstable_composeClasses"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/base/composeClasses/index.js"], "sourcesContent": ["export { unstable_composeClasses } from '@mui/utils';"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}