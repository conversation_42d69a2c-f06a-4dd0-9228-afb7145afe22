{"ast": null, "code": "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getTimelineItemUtilityClass(slot) {\n  return generateUtilityClass('MuiTimelineItem', slot);\n}\nconst timelineItemClasses = generateUtilityClasses('MuiTimelineItem', ['root', 'positionLeft', 'positionRight', 'positionAlternate', 'positionAlternateReverse', 'missingOppositeContent']);\nexport default timelineItemClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTimelineItemUtilityClass", "slot", "timelineItemClasses"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/TimelineItem/timelineItemClasses.js"], "sourcesContent": ["import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getTimelineItemUtilityClass(slot) {\n  return generateUtilityClass('MuiTimelineItem', slot);\n}\nconst timelineItemClasses = generateUtilityClasses('MuiTimelineItem', ['root', 'positionLeft', 'positionRight', 'positionAlternate', 'positionAlternateReverse', 'missingOppositeContent']);\nexport default timelineItemClasses;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,sBAAsB,MAAM,mCAAmC;AACtE,OAAO,SAASC,2BAA2BA,CAACC,IAAI,EAAE;EAChD,OAAOH,oBAAoB,CAAC,iBAAiB,EAAEG,IAAI,CAAC;AACtD;AACA,MAAMC,mBAAmB,GAAGH,sBAAsB,CAAC,iBAAiB,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,eAAe,EAAE,mBAAmB,EAAE,0BAA0B,EAAE,wBAAwB,CAAC,CAAC;AAC3L,eAAeG,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}