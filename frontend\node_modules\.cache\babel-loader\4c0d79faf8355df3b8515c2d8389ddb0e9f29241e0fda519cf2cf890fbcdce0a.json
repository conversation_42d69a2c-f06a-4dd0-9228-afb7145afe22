{"ast": null, "code": "'use client';\n\nexport { useMenu } from './useMenu';\nexport * from './useMenu.types';\nexport * from './MenuProvider';", "map": {"version": 3, "names": ["useMenu"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/base/useMenu/index.js"], "sourcesContent": ["'use client';\n\nexport { useMenu } from './useMenu';\nexport * from './useMenu.types';\nexport * from './MenuProvider';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,WAAW;AACnC,cAAc,iBAAiB;AAC/B,cAAc,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}