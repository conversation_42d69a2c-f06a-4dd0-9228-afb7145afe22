import React, { useState, useEffect } from 'react';
import { 
  Table, TableBody, TableCell, TableContainer, 
  TableHead, TableRow, Paper, Button, TextField,
  Dialog, DialogTitle, DialogContent, DialogActions
} from '@mui/material';
import axios from 'axios';

const MaterialCatalog = () => {
  const [materials, setMaterials] = useState([]);
  const [open, setOpen] = useState(false);
  const [newMaterial, setNewMaterial] = useState({
    sku: '',
    name: '',
    description: '',
    unit_of_measure: '',
    base_cost_per_unit: '',
    category: ''
  });

  useEffect(() => {
    const fetchMaterials = async () => {
      const res = await axios.get('/api/materials');
      setMaterials(res.data);
    };
    fetchMaterials();
  }, []);

  const handleAddMaterial = async () => {
    try {
      const res = await axios.post('/api/materials', newMaterial);
      setMaterials([...materials, res.data]);
      setOpen(false);
    } catch (err) {
      console.error(err);
    }
  };

  // Render material catalog table with search, filter, and add functionality
  return (
    <div>
      <h2>Material Catalog</h2>
      <Button variant="contained" color="primary" onClick={() => setOpen(true)}>
        Add New Material
      </Button>
      
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>SKU</TableCell>
              <TableCell>Name</TableCell>
              <TableCell>Description</TableCell>
              <TableCell>Unit</TableCell>
              <TableCell>Cost</TableCell>
              <TableCell>Category</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {materials.map(material => (
              <TableRow key={material.id}>
                <TableCell>{material.sku}</TableCell>
                <TableCell>{material.name}</TableCell>
                <TableCell>{material.description}</TableCell>
                <TableCell>{material.unit_of_measure}</TableCell>
                <TableCell>${material.base_cost_per_unit}</TableCell>
                <TableCell>{material.category}</TableCell>
                <TableCell>
                  <Button size="small">Edit</Button>
                  <Button size="small">Variants</Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      
      {/* Dialog for adding new material */}
      <Dialog open={open} onClose={() => setOpen(false)}>
        <DialogTitle>Add New Material</DialogTitle>
        <DialogContent>
          {/* Form fields for new material */}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpen(false)}>Cancel</Button>
          <Button onClick={handleAddMaterial} color="primary">Add</Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default MaterialCatalog;