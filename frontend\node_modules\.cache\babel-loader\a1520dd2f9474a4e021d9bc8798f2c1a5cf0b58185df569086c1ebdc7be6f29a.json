{"ast": null, "code": "'use client';\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The CalendarPicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { CalendarPicker } from '@mui/x-date-pickers'`\", \"or `import { CalendarPicker } from '@mui/x-date-pickers/CalendarPicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The CalendarPicker component was moved from `@mui/lab` to `@mui/x-date-pickers`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst CalendarPicker = /*#__PURE__*/React.forwardRef(function DeprecatedCalendarPicker() {\n  warn();\n  return null;\n});\nexport default CalendarPicker;\nexport const calendarPickerClasses = {};", "map": {"version": 3, "names": ["React", "warnedOnce", "warn", "console", "join", "CalendarPicker", "forwardRef", "DeprecatedCalendarPicker", "calendarPickerClasses"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/CalendarPicker/CalendarPicker.js"], "sourcesContent": ["'use client';\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The CalendarPicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { CalendarPicker } from '@mui/x-date-pickers'`\", \"or `import { CalendarPicker } from '@mui/x-date-pickers/CalendarPicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The CalendarPicker component was moved from `@mui/lab` to `@mui/x-date-pickers`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst CalendarPicker = /*#__PURE__*/React.forwardRef(function DeprecatedCalendarPicker() {\n  warn();\n  return null;\n});\nexport default CalendarPicker;\nexport const calendarPickerClasses = {};"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,UAAU,GAAG,KAAK;AACtB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,IAAI,CAACD,UAAU,EAAE;IACfE,OAAO,CAACD,IAAI,CAAC,CAAC,uFAAuF,EAAE,EAAE,EAAE,uEAAuE,EAAE,0EAA0E,EAAE,EAAE,EAAE,qGAAqG,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;IACtXH,UAAU,GAAG,IAAI;EACnB;AACF,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMI,cAAc,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,SAASC,wBAAwBA,CAAA,EAAG;EACvFL,IAAI,CAAC,CAAC;EACN,OAAO,IAAI;AACb,CAAC,CAAC;AACF,eAAeG,cAAc;AAC7B,OAAO,MAAMG,qBAAqB,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}