-- Sample data for Boynton Beach, FL area construction materials system

-- Insert material categories
INSERT INTO material_categories (name, description, icon) VALUES
('Aggregates & Base Materials', 'Crushed stone, concrete, and base course materials', '🪨'),
('Sand & Soil Products', 'Various sand types and soil products', '🏗️'),
('Recycled / Specialty Materials', 'Recycled and specialty construction materials', '♻️'),
('Landscaping & Decorative', 'Decorative stones and landscaping materials', '🌴'),
('Dust & Fines', 'Fine materials and dust control products', '🌫️'),
('Bulk Liquids & Additives', 'Liquid materials and additives', '💧'),
('Haul-Off / Disposal Types', 'Waste removal and disposal services', '🗑️');

-- Insert unit measures
INSERT INTO unit_measures (name, abbreviation, type) VALUES
('Tons', 'ton', 'weight'),
('Cubic Yards', 'cy', 'volume'),
('Square Feet', 'sqft', 'area'),
('Linear Feet', 'lf', 'length'),
('Loads', 'load', 'count'),
('Gallons', 'gal', 'volume'),
('Pounds', 'lb', 'weight'),
('Each', 'ea', 'count');

-- Insert comprehensive materials list
INSERT INTO materials (sku, name, description, category_id, default_unit_measure_id, base_cost_per_unit, allow_unit_override) VALUES
-- Aggregates & Base Materials
('AGG-001', 'Crushed Concrete', 'Recycled crushed concrete aggregate', 1, 1, 18.50, true),
('AGG-002', 'Crushed Asphalt', 'Recycled crushed asphalt', 1, 1, 16.75, true),
('AGG-003', 'ABC (Aggregate Base Course)', 'Standard aggregate base course', 1, 1, 22.00, true),
('AGG-004', 'Road Base', 'Compactable road base material', 1, 1, 20.50, true),
('AGG-005', '#57 Stone', '3/4 inch crushed stone', 1, 1, 28.00, true),
('AGG-006', '#89 Stone', '3/8 inch crushed stone', 1, 1, 30.00, true),
('AGG-007', 'Rip Rap 6-12 inch', 'Large stone for erosion control', 1, 1, 45.00, true),
('AGG-008', 'Pea Gravel', 'Small decorative gravel', 1, 1, 35.00, true),
('AGG-009', 'Fill Rock', 'Large fill rock material', 1, 1, 15.00, true),
('AGG-010', 'Oversized Rock', 'Extra large rock material', 1, 1, 12.00, true),

-- Sand & Soil Products
('SAND-001', 'Fill Sand', 'General purpose fill sand', 2, 1, 18.00, true),
('SAND-002', 'Mason Sand', 'Fine sand for masonry work', 2, 1, 25.00, true),
('SAND-003', 'Concrete Sand', 'Washed sand for concrete', 2, 1, 22.00, true),
('SAND-004', 'Screened Sand', 'Screened fine sand', 2, 1, 24.00, true),
('SOIL-001', 'Topsoil - Unscreened', 'Natural unscreened topsoil', 2, 2, 28.00, true),
('SOIL-002', 'Topsoil - Screened', 'Screened premium topsoil', 2, 2, 35.00, true),
('SOIL-003', 'Organic Topsoil', 'Organic enriched topsoil', 2, 2, 42.00, true),
('SOIL-004', 'Sandy Loam', 'Sandy loam soil mix', 2, 2, 32.00, true),
('SOIL-005', 'Clay - Red', 'Red clay material', 2, 1, 20.00, true),
('SOIL-006', 'Clay - Gray', 'Gray clay material', 2, 1, 20.00, true),
('SAND-005', 'Stabilized Sand', 'Cement stabilized sand', 2, 1, 30.00, true),

-- Recycled / Specialty Materials
('REC-001', 'Recycled Asphalt Millings', 'RAP millings for base', 3, 1, 14.00, true),
('REC-002', 'Recycled Concrete Base', 'Crushed concrete base', 3, 1, 16.50, true),
('REC-003', 'RAP (Reclaimed Asphalt Pavement)', 'Reclaimed asphalt pavement', 3, 1, 15.75, true),
('SPEC-001', 'Flowable Fill (CLSM)', 'Controlled low strength material', 3, 2, 85.00, true),
('HAUL-001', 'Muck (Haul-off)', 'Muck removal service', 7, 5, 25.00, false),
('HAUL-002', 'Spoil Dirt', 'Excess dirt removal', 7, 5, 20.00, false),
('HAUL-003', 'Contaminated Soil', 'Contaminated soil disposal', 7, 1, 45.00, false),
('SPEC-002', 'Lime Rock Base', 'Limestone base material', 3, 1, 19.50, true),
('SPEC-003', 'Shell Rock', 'Natural shell rock', 3, 1, 24.00, true),

-- Landscaping & Decorative
('LAND-001', 'Mulch - Natural', 'Natural wood mulch', 4, 2, 32.00, true),
('LAND-002', 'Mulch - Red', 'Red colored mulch', 4, 2, 38.00, true),
('LAND-003', 'Mulch - Brown', 'Brown colored mulch', 4, 2, 38.00, true),
('LAND-004', 'Mulch - Black', 'Black colored mulch', 4, 2, 38.00, true),
('LAND-005', 'River Rock - Small', 'Small decorative river rock', 4, 1, 55.00, true),
('LAND-006', 'River Rock - Medium', 'Medium decorative river rock', 4, 1, 60.00, true),
('LAND-007', 'River Rock - Large', 'Large decorative river rock', 4, 1, 65.00, true),
('LAND-008', 'Lava Rock', 'Decorative lava rock', 4, 1, 75.00, true),
('LAND-009', 'Pine Bark Nuggets', 'Large pine bark pieces', 4, 2, 45.00, true),
('LAND-010', 'Potting Soil Mix', 'Premium potting soil', 4, 2, 85.00, true),

-- Dust & Fines
('DUST-001', 'Rock Fines', 'Fine rock dust', 5, 1, 12.00, true),
('DUST-002', 'Granite Fines', 'Granite screening fines', 5, 1, 15.00, true),
('DUST-003', 'Limestone Dust', 'Limestone screening dust', 5, 1, 14.00, true),
('DUST-004', 'Screening Material', 'General screening material', 5, 1, 13.00, true),

-- Bulk Liquids & Additives
('LIQ-001', 'Water (Dust Control)', 'Water for dust suppression', 6, 6, 0.15, false),
('LIQ-002', 'Liquid Lime', 'Liquid lime stabilizer', 6, 6, 2.50, false),
('LIQ-003', 'Slurry Cement', 'Cement slurry mix', 6, 6, 8.75, false),

-- Additional Haul-Off Services
('HAUL-004', 'Concrete Demo (Broken)', 'Broken concrete removal', 7, 5, 22.00, false),
('HAUL-005', 'Asphalt Demo', 'Asphalt removal service', 7, 5, 24.00, false),
('HAUL-006', 'Mixed Construction Debris', 'General construction waste', 7, 5, 35.00, false),
('HAUL-007', 'Tree Debris / Vegetation', 'Vegetation removal', 7, 5, 28.00, false),
('HAUL-008', 'Excess Dirt - Clean', 'Clean dirt removal', 7, 5, 18.00, false),
('HAUL-009', 'Excess Dirt - Contaminated', 'Contaminated dirt removal', 7, 5, 42.00, false);

-- Insert material unit pricing (alternative pricing for different units)
INSERT INTO material_unit_pricing (material_id, unit_measure_id, price_per_unit, is_default) VALUES
-- Tons to Cubic Yards conversions (approximate)
(1, 2, 14.50, false), -- Crushed Concrete in CY
(2, 2, 13.25, false), -- Crushed Asphalt in CY
(3, 2, 17.50, false), -- ABC in CY
(5, 2, 22.00, false), -- #57 Stone in CY
(6, 2, 24.00, false), -- #89 Stone in CY
-- Load pricing for haul-off services
(25, 5, 150.00, true), -- Muck haul-off per load
(26, 5, 120.00, true), -- Spoil dirt per load
(39, 5, 180.00, true), -- Concrete demo per load
(40, 5, 200.00, true); -- Asphalt demo per load

-- Insert sample customers in Boynton Beach area
INSERT INTO customers (company_name, contact_name, email, phone, credit_terms) VALUES
('Boynton Beach Construction LLC', 'Michael Rodriguez', '<EMAIL>', '************', 'NET30'),
('Palm Beach Builders', 'Sarah Thompson', '<EMAIL>', '************', 'NET15'),
('Coastal Development Group', 'David Chen', '<EMAIL>', '************', 'COD'),
('Renaissance Commons Contractors', 'Lisa Martinez', '<EMAIL>', '************', 'NET30'),
('South Florida Infrastructure', 'James Wilson', '<EMAIL>', '************', 'NET15');

-- Insert sample drivers
INSERT INTO drivers (name, email, phone, license_number, status) VALUES
('Carlos Rodriguez', '<EMAIL>', '************', 'CDL-FL-123456', 'Active'),
('Maria Gonzalez', '<EMAIL>', '************', 'CDL-FL-789012', 'Active'),
('Robert Johnson', '<EMAIL>', '************', 'CDL-FL-345678', 'Active'),
('Jennifer Davis', '<EMAIL>', '************', 'CDL-FL-567890', 'Active');

-- Insert sample trucks
INSERT INTO trucks (license_plate, make, model, year, capacity_tons, status) VALUES
('FL-TRK-001', 'Peterbilt', '379', 2020, 25.0, 'Available'),
('FL-TRK-002', 'Kenworth', 'T800', 2019, 30.0, 'Available'),
('FL-TRK-003', 'Freightliner', 'Cascadia', 2021, 28.0, 'Available'),
('FL-TRK-004', 'Mack', 'Granite', 2022, 32.0, 'Available');

-- Insert geofences for Boynton Beach area (using actual coordinates)
INSERT INTO geofences (name, description, latitude, longitude, radius_meters, type, address, is_active) VALUES
('Main Quarry - West Palm Beach', 'Primary aggregate pickup location', 26.7153, -80.0534, 200, 'pickup', '2500 Industrial Blvd, West Palm Beach, FL 33407', true),
('Boynton Beach Sand Pit', 'Secondary sand pickup location', 26.5312, -80.0728, 180, 'pickup', '1800 N Congress Ave, Boynton Beach, FL 33426', true),
('Renaissance Commons Site', 'Main project delivery location', 26.5284, -80.0831, 150, 'dropoff', '1605 Renaissance Commons Blvd, Boynton Beach, FL 33426', true),
('Delray Beach Development', 'Residential development project', 26.4615, -80.0728, 120, 'dropoff', '2200 Linton Blvd, Delray Beach, FL 33445', true),
('Boca Raton Commercial Site', 'Commercial construction site', 26.3683, -80.1289, 130, 'dropoff', '1500 NW 2nd Ave, Boca Raton, FL 33432', true),
('Lake Worth Infrastructure', 'Road improvement project', 26.6156, -80.0670, 140, 'dropoff', '1900 2nd Ave N, Lake Worth, FL 33461', true),
('Wellington Residential', 'New residential community', 26.6581, -80.2411, 160, 'dropoff', '12800 Forest Hill Blvd, Wellington, FL 33414', true);

-- Insert sample purchase orders with auto-generated PO numbers
INSERT INTO purchase_orders (customer_id, status, pickup_location, pickup_latitude, pickup_longitude, notes, created_by) VALUES
(1, 'Active', 'Main Quarry - 2500 Industrial Blvd, West Palm Beach, FL', 26.7153, -80.0534, 'Renaissance Commons development project - multiple deliveries', '<EMAIL>'),
(2, 'Active', 'Boynton Beach Sand Pit - 1800 N Congress Ave, Boynton Beach, FL', 26.5312, -80.0728, 'Delray Beach residential project', '<EMAIL>'),
(3, 'Active', 'Main Quarry - 2500 Industrial Blvd, West Palm Beach, FL', 26.7153, -80.0534, 'Boca Raton commercial development', '<EMAIL>'),
(4, 'Draft', 'Boynton Beach Sand Pit - 1800 N Congress Ave, Boynton Beach, FL', 26.5312, -80.0728, 'Lake Worth road improvements', '<EMAIL>'),
(5, 'Active', 'Main Quarry - 2500 Industrial Blvd, West Palm Beach, FL', 26.7153, -80.0534, 'Wellington residential community', '<EMAIL>');

-- Insert purchase order line items with different hauling rates
INSERT INTO po_line_items (po_id, material_id, unit_measure_id, quantity, unit_price, hauling_rate, dropoff_location, dropoff_latitude, dropoff_longitude, special_instructions) VALUES
-- PO 1: Renaissance Commons project (3 deliveries)
(1, 3, 1, 50.0, 22.00, 15.00, '1605 Renaissance Commons Blvd, Boynton Beach, FL 33426', 26.5284, -80.0831, 'Deliver to north entrance, contact site foreman'),
(1, 5, 1, 25.0, 28.00, 18.00, '1605 Renaissance Commons Blvd, Boynton Beach, FL 33426', 26.5284, -80.0831, 'For foundation work - priority delivery'),
(1, 11, 1, 30.0, 18.00, 12.00, '1605 Renaissance Commons Blvd, Boynton Beach, FL 33426', 26.5284, -80.0831, 'Backfill material for utilities'),

-- PO 2: Delray Beach residential (2 deliveries)
(2, 13, 1, 40.0, 25.00, 14.00, '2200 Linton Blvd, Delray Beach, FL 33445', 26.4615, -80.0728, 'Masonry work - deliver AM only'),
(2, 16, 2, 15.0, 35.00, 20.00, '2200 Linton Blvd, Delray Beach, FL 33445', 26.4615, -80.0728, 'Landscaping topsoil - spread upon delivery'),

-- PO 3: Boca Raton commercial (3 deliveries)
(3, 1, 1, 75.0, 18.50, 16.00, '1500 NW 2nd Ave, Boca Raton, FL 33432', 26.3683, -80.1289, 'Parking lot base - compact after delivery'),
(3, 4, 1, 35.0, 20.50, 14.00, '1500 NW 2nd Ave, Boca Raton, FL 33432', 26.3683, -80.1289, 'Road base for access road'),
(3, 6, 1, 20.0, 30.00, 22.00, '1500 NW 2nd Ave, Boca Raton, FL 33432', 26.3683, -80.1289, 'Decorative stone for entrance'),

-- PO 4: Lake Worth infrastructure (2 deliveries)
(4, 2, 1, 60.0, 16.75, 13.00, '1900 2nd Ave N, Lake Worth, FL 33461', 26.6156, -80.0670, 'Road repair material - coordinate with city'),
(4, 3, 1, 45.0, 22.00, 15.00, '1900 2nd Ave N, Lake Worth, FL 33461', 26.6156, -80.0670, 'Base course for road reconstruction'),

-- PO 5: Wellington residential (3 deliveries)
(5, 11, 1, 80.0, 18.00, 25.00, '12800 Forest Hill Blvd, Wellington, FL 33414', 26.6581, -80.2411, 'Fill sand for lot preparation - long haul rate'),
(5, 16, 2, 25.0, 35.00, 28.00, '12800 Forest Hill Blvd, Wellington, FL 33414', 26.6581, -80.2411, 'Premium topsoil for landscaping - long haul'),
(5, 27, 2, 10.0, 32.00, 30.00, '12800 Forest Hill Blvd, Wellington, FL 33414', 26.6581, -80.2411, 'Sandy loam for gardens - long haul rate');

-- Create deliveries from active PO line items
INSERT INTO deliveries (
  po_line_item_id, po_id, pickup_location, dropoff_location,
  pickup_latitude, pickup_longitude, dropoff_latitude, dropoff_longitude,
  scheduled_date, preferred_time_window, status, driver_id, truck_id,
  material_name, quantity, unit_measure, hauling_rate, special_instructions
) VALUES
-- PO 1 deliveries (Renaissance Commons)
(1, 1, 'Main Quarry - 2500 Industrial Blvd, West Palm Beach, FL', '1605 Renaissance Commons Blvd, Boynton Beach, FL 33426',
 26.7153, -80.0534, 26.5284, -80.0831, CURRENT_DATE, '08:00-12:00', 'Scheduled', 1, 1,
 'ABC (Aggregate Base Course)', 50.0, 'ton', 15.00, 'Deliver to north entrance, contact site foreman'),
(2, 1, 'Main Quarry - 2500 Industrial Blvd, West Palm Beach, FL', '1605 Renaissance Commons Blvd, Boynton Beach, FL 33426',
 26.7153, -80.0534, 26.5284, -80.0831, CURRENT_DATE, '13:00-17:00', 'Scheduled', 2, 2,
 '#57 Stone', 25.0, 'ton', 18.00, 'For foundation work - priority delivery'),

-- PO 2 deliveries (Delray Beach)
(4, 2, 'Boynton Beach Sand Pit - 1800 N Congress Ave, Boynton Beach, FL', '2200 Linton Blvd, Delray Beach, FL 33445',
 26.5312, -80.0728, 26.4615, -80.0728, CURRENT_DATE + 1, '07:00-11:00', 'Scheduled', 3, 3,
 'Mason Sand', 40.0, 'ton', 14.00, 'Masonry work - deliver AM only'),

-- PO 3 deliveries (Boca Raton)
(6, 3, 'Main Quarry - 2500 Industrial Blvd, West Palm Beach, FL', '1500 NW 2nd Ave, Boca Raton, FL 33432',
 26.7153, -80.0534, 26.3683, -80.1289, CURRENT_DATE + 1, '08:00-16:00', 'En Route', 4, 4,
 'Crushed Concrete', 75.0, 'ton', 16.00, 'Parking lot base - compact after delivery');

-- Insert some sample geofence events for testing
INSERT INTO geofence_events (delivery_id, geofence_id, driver_id, event_type, latitude, longitude, auto_status_update) VALUES
(4, 1, 4, 'enter', 26.7153, -80.0534, 'At Pickup'),
(4, 1, 4, 'exit', 26.7153, -80.0534, 'Loaded');

-- Insert some sample location updates
INSERT INTO location_updates (delivery_id, driver_id, latitude, longitude, accuracy_meters, speed_kmh, heading_degrees) VALUES
(4, 4, 26.7153, -80.0534, 5.0, 0.0, 180),
(4, 4, 26.6800, -80.0600, 8.0, 45.0, 195),
(4, 4, 26.6500, -80.0700, 6.0, 50.0, 210);
