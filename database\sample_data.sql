-- Sample data for testing the geofencing functionality

-- Insert sample materials
INSERT INTO materials (sku, name, description, unit_of_measure, base_cost_per_unit, category) VALUES
('SAND-001', 'Construction Sand', 'Fine sand for construction', 'ton', 25.00, 'Sand'),
('GRAVEL-001', 'Crushed Gravel', '3/4 inch crushed gravel', 'ton', 30.00, 'Gravel'),
('CONCRETE-001', 'Ready Mix Concrete', 'Standard concrete mix', 'cubic_yard', 120.00, 'Concrete');

-- Insert sample customers
INSERT INTO customers (company_name, contact_name, email, phone, credit_terms) VALUES
('ABC Construction', '<PERSON>', '<EMAIL>', '555-0101', 'NET30'),
('XYZ Builders', '<PERSON>', '<EMAIL>', '555-0102', 'COD'),
('Metro Development', '<PERSON>', '<EMAIL>', '555-0103', 'NET15');

-- Insert sample drivers
INSERT INTO drivers (name, email, phone, license_number, status) VALUES
('<PERSON>', '<EMAIL>', '555-0201', 'CDL123456', 'Active'),
('<PERSON> Davis', '<EMAIL>', '555-0202', 'CDL789012', 'Active'),
('Tom Brown', '<EMAIL>', '555-0203', 'CDL345678', 'Active');

-- Insert sample trucks
INSERT INTO trucks (license_plate, make, model, year, capacity_tons, status) VALUES
('TRK-001', 'Peterbilt', '379', 2020, 25.0, 'Available'),
('TRK-002', 'Kenworth', 'T800', 2019, 30.0, 'Available'),
('TRK-003', 'Freightliner', 'Cascadia', 2021, 28.0, 'Available');

-- Insert sample orders
INSERT INTO orders (customer_id, status, subtotal, tax, delivery_fee, total) VALUES
(1, 'Confirmed', 500.00, 40.00, 50.00, 590.00),
(2, 'Confirmed', 750.00, 60.00, 75.00, 885.00),
(3, 'Confirmed', 300.00, 24.00, 30.00, 354.00);

-- Insert sample order items
INSERT INTO order_items (order_id, material_id, quantity, unit_price, discount_percent) VALUES
(1, 1, 20.0, 25.00, 0),
(2, 2, 25.0, 30.00, 0),
(3, 1, 12.0, 25.00, 0);

-- Insert sample geofences (using coordinates around a fictional city)
INSERT INTO geofences (name, description, latitude, longitude, radius_meters, type, address, is_active) VALUES
('Main Quarry', 'Primary sand and gravel pickup location', 40.7589, -73.9851, 200, 'pickup', '123 Quarry Road, Industrial District', true),
('Downtown Construction Site', 'Major downtown development project', 40.7505, -73.9934, 150, 'dropoff', '456 Main Street, Downtown', true),
('Westside Depot', 'Secondary pickup location for concrete', 40.7614, -73.9776, 180, 'pickup', '789 West Avenue, Industrial Park', true),
('Riverside Project', 'Waterfront development site', 40.7282, -74.0776, 120, 'dropoff', '321 River Road, Waterfront', true),
('Central Disposal Site', 'Waste material disposal facility', 40.7831, -73.9712, 250, 'disposal', '555 Disposal Lane, North District', true);

-- Insert sample deliveries with coordinates
INSERT INTO deliveries (
  order_id, pickup_location, dropoff_location, 
  pickup_latitude, pickup_longitude, dropoff_latitude, dropoff_longitude,
  scheduled_date, preferred_time_window, status, driver_id, truck_id
) VALUES
(1, 'Main Quarry - 123 Quarry Road', 'Downtown Construction Site - 456 Main Street', 
 40.7589, -73.9851, 40.7505, -73.9934,
 CURRENT_DATE, '08:00-12:00', 'Scheduled', 1, 1),
(2, 'Westside Depot - 789 West Avenue', 'Riverside Project - 321 River Road', 
 40.7614, -73.9776, 40.7282, -74.0776,
 CURRENT_DATE, '13:00-17:00', 'En Route', 2, 2),
(3, 'Main Quarry - 123 Quarry Road', 'Downtown Construction Site - 456 Main Street', 
 40.7589, -73.9851, 40.7505, -73.9934,
 CURRENT_DATE + 1, '09:00-15:00', 'Scheduled', NULL, NULL);

-- Insert some sample geofence events for testing
INSERT INTO geofence_events (delivery_id, geofence_id, driver_id, event_type, latitude, longitude, auto_status_update) VALUES
(2, 3, 2, 'enter', 40.7614, -73.9776, 'At Pickup'),
(2, 3, 2, 'exit', 40.7614, -73.9776, 'Loaded');

-- Insert some sample location updates
INSERT INTO location_updates (delivery_id, driver_id, latitude, longitude, accuracy_meters, speed_kmh, heading_degrees) VALUES
(2, 2, 40.7614, -73.9776, 5.0, 0.0, 180),
(2, 2, 40.7600, -73.9800, 8.0, 25.0, 195),
(2, 2, 40.7550, -73.9850, 6.0, 30.0, 210);
