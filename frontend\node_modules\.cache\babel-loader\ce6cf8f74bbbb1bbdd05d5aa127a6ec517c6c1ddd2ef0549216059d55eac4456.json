{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { ListActionTypes, listReducer } from '../useList';\nexport function menuReducer(state, action) {\n  if (action.type === ListActionTypes.itemHover) {\n    return _extends({}, state, {\n      highlightedValue: action.item\n    });\n  }\n  const newState = listReducer(state, action);\n\n  // make sure an item is always highlighted\n  if (newState.highlightedValue === null && action.context.items.length > 0) {\n    return _extends({}, newState, {\n      highlightedValue: action.context.items[0]\n    });\n  }\n  if (action.type === ListActionTypes.keyDown) {\n    if (action.event.key === 'Escape') {\n      return _extends({}, newState, {\n        open: false\n      });\n    }\n  }\n  if (action.type === ListActionTypes.blur) {\n    var _action$context$listb;\n    if (!((_action$context$listb = action.context.listboxRef.current) != null && _action$context$listb.contains(action.event.relatedTarget))) {\n      var _action$context$listb2, _action$event$related;\n      // To prevent the menu from closing when the focus leaves the menu to the button.\n      // For more details, see https://github.com/mui/material-ui/pull/36917#issuecomment-1566992698\n      const listboxId = (_action$context$listb2 = action.context.listboxRef.current) == null ? void 0 : _action$context$listb2.getAttribute('id');\n      const controlledBy = (_action$event$related = action.event.relatedTarget) == null ? void 0 : _action$event$related.getAttribute('aria-controls');\n      if (listboxId && controlledBy && listboxId === controlledBy) {\n        return newState;\n      }\n      return _extends({}, newState, {\n        open: false,\n        highlightedValue: action.context.items[0]\n      });\n    }\n  }\n  return newState;\n}", "map": {"version": 3, "names": ["_extends", "ListActionTypes", "listReducer", "menuReducer", "state", "action", "type", "itemHover", "highlightedValue", "item", "newState", "context", "items", "length", "keyDown", "event", "key", "open", "blur", "_action$context$listb", "listboxRef", "current", "contains", "relatedTarget", "_action$context$listb2", "_action$event$related", "listboxId", "getAttribute", "controlledBy"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/base/useMenu/menuReducer.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { ListActionTypes, listReducer } from '../useList';\nexport function menuReducer(state, action) {\n  if (action.type === ListActionTypes.itemHover) {\n    return _extends({}, state, {\n      highlightedValue: action.item\n    });\n  }\n  const newState = listReducer(state, action);\n\n  // make sure an item is always highlighted\n  if (newState.highlightedValue === null && action.context.items.length > 0) {\n    return _extends({}, newState, {\n      highlightedValue: action.context.items[0]\n    });\n  }\n  if (action.type === ListActionTypes.keyDown) {\n    if (action.event.key === 'Escape') {\n      return _extends({}, newState, {\n        open: false\n      });\n    }\n  }\n  if (action.type === ListActionTypes.blur) {\n    var _action$context$listb;\n    if (!((_action$context$listb = action.context.listboxRef.current) != null && _action$context$listb.contains(action.event.relatedTarget))) {\n      var _action$context$listb2, _action$event$related;\n      // To prevent the menu from closing when the focus leaves the menu to the button.\n      // For more details, see https://github.com/mui/material-ui/pull/36917#issuecomment-1566992698\n      const listboxId = (_action$context$listb2 = action.context.listboxRef.current) == null ? void 0 : _action$context$listb2.getAttribute('id');\n      const controlledBy = (_action$event$related = action.event.relatedTarget) == null ? void 0 : _action$event$related.getAttribute('aria-controls');\n      if (listboxId && controlledBy && listboxId === controlledBy) {\n        return newState;\n      }\n      return _extends({}, newState, {\n        open: false,\n        highlightedValue: action.context.items[0]\n      });\n    }\n  }\n  return newState;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,eAAe,EAAEC,WAAW,QAAQ,YAAY;AACzD,OAAO,SAASC,WAAWA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACzC,IAAIA,MAAM,CAACC,IAAI,KAAKL,eAAe,CAACM,SAAS,EAAE;IAC7C,OAAOP,QAAQ,CAAC,CAAC,CAAC,EAAEI,KAAK,EAAE;MACzBI,gBAAgB,EAAEH,MAAM,CAACI;IAC3B,CAAC,CAAC;EACJ;EACA,MAAMC,QAAQ,GAAGR,WAAW,CAACE,KAAK,EAAEC,MAAM,CAAC;;EAE3C;EACA,IAAIK,QAAQ,CAACF,gBAAgB,KAAK,IAAI,IAAIH,MAAM,CAACM,OAAO,CAACC,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;IACzE,OAAOb,QAAQ,CAAC,CAAC,CAAC,EAAEU,QAAQ,EAAE;MAC5BF,gBAAgB,EAAEH,MAAM,CAACM,OAAO,CAACC,KAAK,CAAC,CAAC;IAC1C,CAAC,CAAC;EACJ;EACA,IAAIP,MAAM,CAACC,IAAI,KAAKL,eAAe,CAACa,OAAO,EAAE;IAC3C,IAAIT,MAAM,CAACU,KAAK,CAACC,GAAG,KAAK,QAAQ,EAAE;MACjC,OAAOhB,QAAQ,CAAC,CAAC,CAAC,EAAEU,QAAQ,EAAE;QAC5BO,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EACF;EACA,IAAIZ,MAAM,CAACC,IAAI,KAAKL,eAAe,CAACiB,IAAI,EAAE;IACxC,IAAIC,qBAAqB;IACzB,IAAI,EAAE,CAACA,qBAAqB,GAAGd,MAAM,CAACM,OAAO,CAACS,UAAU,CAACC,OAAO,KAAK,IAAI,IAAIF,qBAAqB,CAACG,QAAQ,CAACjB,MAAM,CAACU,KAAK,CAACQ,aAAa,CAAC,CAAC,EAAE;MACxI,IAAIC,sBAAsB,EAAEC,qBAAqB;MACjD;MACA;MACA,MAAMC,SAAS,GAAG,CAACF,sBAAsB,GAAGnB,MAAM,CAACM,OAAO,CAACS,UAAU,CAACC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGG,sBAAsB,CAACG,YAAY,CAAC,IAAI,CAAC;MAC3I,MAAMC,YAAY,GAAG,CAACH,qBAAqB,GAAGpB,MAAM,CAACU,KAAK,CAACQ,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,qBAAqB,CAACE,YAAY,CAAC,eAAe,CAAC;MAChJ,IAAID,SAAS,IAAIE,YAAY,IAAIF,SAAS,KAAKE,YAAY,EAAE;QAC3D,OAAOlB,QAAQ;MACjB;MACA,OAAOV,QAAQ,CAAC,CAAC,CAAC,EAAEU,QAAQ,EAAE;QAC5BO,IAAI,EAAE,KAAK;QACXT,gBAAgB,EAAEH,MAAM,CAACM,OAAO,CAACC,KAAK,CAAC,CAAC;MAC1C,CAAC,CAAC;IACJ;EACF;EACA,OAAOF,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}