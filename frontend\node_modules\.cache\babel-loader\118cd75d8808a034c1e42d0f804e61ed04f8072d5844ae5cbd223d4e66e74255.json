{"ast": null, "code": "import React,{createContext,useContext,useState}from'react';import{jsx as _jsx}from\"react/jsx-runtime\";const LanguageContext=/*#__PURE__*/createContext();export const useLanguage=()=>{const context=useContext(LanguageContext);if(!context){throw new Error('useLanguage must be used within a LanguageProvider');}return context;};const translations={en:{// Navigation\n'dispatch_dashboard':'Dispatch Dashboard','purchase_orders':'Purchase Orders','driver_portal':'Driver Portal','geofences':'Geofences','admin_portal':'Admin Portal','logout':'Logout',// Purchase Orders\n'create_purchase_order':'Create Purchase Order','order_information':'Order Information','client':'Client','pickup_location':'Pickup Location','notes':'Notes','line_items':'Line Items','material':'Material','quantity':'Quantity','unit':'Unit','unit_price':'Unit Price','hauling_rate':'Hauling Rate','dropoff_location':'Dropoff Location','special_instructions':'Special Instructions','add_line_item':'Add Line Item','total':'Total','create':'Create','creating':'Creating...','line_total':'Line Total',// Driver Portal\n'driver_dashboard':'Driver Dashboard','my_deliveries':'My Deliveries','location_tracking_active':'Location Tracking Active','location_disabled':'Location Disabled','no_deliveries_assigned':'No deliveries assigned','start_route':'Start Route','mark_loaded':'Mark Loaded','in_transit':'In Transit','mark_delivered':'Mark Delivered','confirm_delivery':'Confirm Delivery','location_required':'Location required for delivery',// Login\n'driver_login':'Driver Login','trucker_login':'Trucker Login','admin_login':'Admin Login','email_address':'Email Address','password':'Password','sign_in':'Sign In','demo_credentials':'Demo Credentials',// Common\n'save':'Save','cancel':'Cancel','edit':'Edit','delete':'Delete','add':'Add','loading':'Loading...','error':'Error','success':'Success','status':'Status','scheduled':'Scheduled','en_route':'En Route','delivered':'Delivered','pickup':'Pickup','dropoff':'Dropoff'},es:{// Navigation\n'dispatch_dashboard':'Panel de Despacho','purchase_orders':'Órdenes de Compra','driver_portal':'Portal del Conductor','geofences':'Geocercas','admin_portal':'Portal de Administrador','logout':'Cerrar Sesión',// Purchase Orders\n'create_purchase_order':'Crear Orden de Compra','order_information':'Información del Pedido','client':'Cliente','pickup_location':'Ubicación de Recogida','notes':'Notas','line_items':'Artículos de Línea','material':'Material','quantity':'Cantidad','unit':'Unidad','unit_price':'Precio Unitario','hauling_rate':'Tarifa de Transporte','dropoff_location':'Ubicación de Entrega','special_instructions':'Instrucciones Especiales','add_line_item':'Agregar Artículo','total':'Total','create':'Crear','creating':'Creando...','line_total':'Total de Línea',// Driver Portal\n'driver_dashboard':'Panel del Conductor','my_deliveries':'Mis Entregas','location_tracking_active':'Seguimiento de Ubicación Activo','location_disabled':'Ubicación Deshabilitada','no_deliveries_assigned':'No hay entregas asignadas','start_route':'Iniciar Ruta','mark_loaded':'Marcar Cargado','in_transit':'En Tránsito','mark_delivered':'Marcar Entregado','confirm_delivery':'Confirmar Entrega','location_required':'Ubicación requerida para entrega',// Login\n'driver_login':'Inicio de Sesión del Conductor','trucker_login':'Inicio de Sesión del Camionero','admin_login':'Inicio de Sesión del Administrador','email_address':'Dirección de Correo','password':'Contraseña','sign_in':'Iniciar Sesión','demo_credentials':'Credenciales de Demostración',// Common\n'save':'Guardar','cancel':'Cancelar','edit':'Editar','delete':'Eliminar','add':'Agregar','loading':'Cargando...','error':'Error','success':'Éxito','status':'Estado','scheduled':'Programado','en_route':'En Camino','delivered':'Entregado','pickup':'Recogida','dropoff':'Entrega'}};export const LanguageProvider=_ref=>{let{children}=_ref;const[language,setLanguage]=useState('en');const t=key=>{return translations[language][key]||key;};const toggleLanguage=()=>{setLanguage(prev=>prev==='en'?'es':'en');};return/*#__PURE__*/_jsx(LanguageContext.Provider,{value:{language,setLanguage,t,toggleLanguage},children:children});};export default LanguageContext;", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "jsx", "_jsx", "LanguageContext", "useLanguage", "context", "Error", "translations", "en", "es", "LanguageProvider", "_ref", "children", "language", "setLanguage", "t", "key", "toggleLanguage", "prev", "Provider", "value"], "sources": ["C:/NewSiteKevin/frontend/src/contexts/LanguageContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState } from 'react';\n\nconst LanguageContext = createContext();\n\nexport const useLanguage = () => {\n  const context = useContext(LanguageContext);\n  if (!context) {\n    throw new Error('useLanguage must be used within a LanguageProvider');\n  }\n  return context;\n};\n\nconst translations = {\n  en: {\n    // Navigation\n    'dispatch_dashboard': 'Dispatch Dashboard',\n    'purchase_orders': 'Purchase Orders',\n    'driver_portal': 'Driver Portal',\n    'geofences': 'Geofences',\n    'admin_portal': 'Admin Portal',\n    'logout': 'Logout',\n    \n    // Purchase Orders\n    'create_purchase_order': 'Create Purchase Order',\n    'order_information': 'Order Information',\n    'client': 'Client',\n    'pickup_location': 'Pickup Location',\n    'notes': 'Notes',\n    'line_items': 'Line Items',\n    'material': 'Material',\n    'quantity': 'Quantity',\n    'unit': 'Unit',\n    'unit_price': 'Unit Price',\n    'hauling_rate': 'Hauling Rate',\n    'dropoff_location': 'Dropoff Location',\n    'special_instructions': 'Special Instructions',\n    'add_line_item': 'Add Line Item',\n    'total': 'Total',\n    'create': 'Create',\n    'creating': 'Creating...',\n    'line_total': 'Line Total',\n    \n    // Driver Portal\n    'driver_dashboard': 'Driver Dashboard',\n    'my_deliveries': 'My Deliveries',\n    'location_tracking_active': 'Location Tracking Active',\n    'location_disabled': 'Location Disabled',\n    'no_deliveries_assigned': 'No deliveries assigned',\n    'start_route': 'Start Route',\n    'mark_loaded': 'Mark Loaded',\n    'in_transit': 'In Transit',\n    'mark_delivered': 'Mark Delivered',\n    'confirm_delivery': 'Confirm Delivery',\n    'location_required': 'Location required for delivery',\n    \n    // Login\n    'driver_login': 'Driver Login',\n    'trucker_login': 'Trucker Login',\n    'admin_login': 'Admin Login',\n    'email_address': 'Email Address',\n    'password': 'Password',\n    'sign_in': 'Sign In',\n    'demo_credentials': 'Demo Credentials',\n    \n    // Common\n    'save': 'Save',\n    'cancel': 'Cancel',\n    'edit': 'Edit',\n    'delete': 'Delete',\n    'add': 'Add',\n    'loading': 'Loading...',\n    'error': 'Error',\n    'success': 'Success',\n    'status': 'Status',\n    'scheduled': 'Scheduled',\n    'en_route': 'En Route',\n    'delivered': 'Delivered',\n    'pickup': 'Pickup',\n    'dropoff': 'Dropoff'\n  },\n  es: {\n    // Navigation\n    'dispatch_dashboard': 'Panel de Despacho',\n    'purchase_orders': 'Órdenes de Compra',\n    'driver_portal': 'Portal del Conductor',\n    'geofences': 'Geocercas',\n    'admin_portal': 'Portal de Administrador',\n    'logout': 'Cerrar Sesión',\n    \n    // Purchase Orders\n    'create_purchase_order': 'Crear Orden de Compra',\n    'order_information': 'Información del Pedido',\n    'client': 'Cliente',\n    'pickup_location': 'Ubicación de Recogida',\n    'notes': 'Notas',\n    'line_items': 'Artículos de Línea',\n    'material': 'Material',\n    'quantity': 'Cantidad',\n    'unit': 'Unidad',\n    'unit_price': 'Precio Unitario',\n    'hauling_rate': 'Tarifa de Transporte',\n    'dropoff_location': 'Ubicación de Entrega',\n    'special_instructions': 'Instrucciones Especiales',\n    'add_line_item': 'Agregar Artículo',\n    'total': 'Total',\n    'create': 'Crear',\n    'creating': 'Creando...',\n    'line_total': 'Total de Línea',\n    \n    // Driver Portal\n    'driver_dashboard': 'Panel del Conductor',\n    'my_deliveries': 'Mis Entregas',\n    'location_tracking_active': 'Seguimiento de Ubicación Activo',\n    'location_disabled': 'Ubicación Deshabilitada',\n    'no_deliveries_assigned': 'No hay entregas asignadas',\n    'start_route': 'Iniciar Ruta',\n    'mark_loaded': 'Marcar Cargado',\n    'in_transit': 'En Tránsito',\n    'mark_delivered': 'Marcar Entregado',\n    'confirm_delivery': 'Confirmar Entrega',\n    'location_required': 'Ubicación requerida para entrega',\n    \n    // Login\n    'driver_login': 'Inicio de Sesión del Conductor',\n    'trucker_login': 'Inicio de Sesión del Camionero',\n    'admin_login': 'Inicio de Sesión del Administrador',\n    'email_address': 'Dirección de Correo',\n    'password': 'Contraseña',\n    'sign_in': 'Iniciar Sesión',\n    'demo_credentials': 'Credenciales de Demostración',\n    \n    // Common\n    'save': 'Guardar',\n    'cancel': 'Cancelar',\n    'edit': 'Editar',\n    'delete': 'Eliminar',\n    'add': 'Agregar',\n    'loading': 'Cargando...',\n    'error': 'Error',\n    'success': 'Éxito',\n    'status': 'Estado',\n    'scheduled': 'Programado',\n    'en_route': 'En Camino',\n    'delivered': 'Entregado',\n    'pickup': 'Recogida',\n    'dropoff': 'Entrega'\n  }\n};\n\nexport const LanguageProvider = ({ children }) => {\n  const [language, setLanguage] = useState('en');\n\n  const t = (key) => {\n    return translations[language][key] || key;\n  };\n\n  const toggleLanguage = () => {\n    setLanguage(prev => prev === 'en' ? 'es' : 'en');\n  };\n\n  return (\n    <LanguageContext.Provider value={{ language, setLanguage, t, toggleLanguage }}>\n      {children}\n    </LanguageContext.Provider>\n  );\n};\n\nexport default LanguageContext;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,UAAU,CAAEC,QAAQ,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAEnE,KAAM,CAAAC,eAAe,cAAGL,aAAa,CAAC,CAAC,CAEvC,MAAO,MAAM,CAAAM,WAAW,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAC,OAAO,CAAGN,UAAU,CAACI,eAAe,CAAC,CAC3C,GAAI,CAACE,OAAO,CAAE,CACZ,KAAM,IAAI,CAAAC,KAAK,CAAC,oDAAoD,CAAC,CACvE,CACA,MAAO,CAAAD,OAAO,CAChB,CAAC,CAED,KAAM,CAAAE,YAAY,CAAG,CACnBC,EAAE,CAAE,CACF;AACA,oBAAoB,CAAE,oBAAoB,CAC1C,iBAAiB,CAAE,iBAAiB,CACpC,eAAe,CAAE,eAAe,CAChC,WAAW,CAAE,WAAW,CACxB,cAAc,CAAE,cAAc,CAC9B,QAAQ,CAAE,QAAQ,CAElB;AACA,uBAAuB,CAAE,uBAAuB,CAChD,mBAAmB,CAAE,mBAAmB,CACxC,QAAQ,CAAE,QAAQ,CAClB,iBAAiB,CAAE,iBAAiB,CACpC,OAAO,CAAE,OAAO,CAChB,YAAY,CAAE,YAAY,CAC1B,UAAU,CAAE,UAAU,CACtB,UAAU,CAAE,UAAU,CACtB,MAAM,CAAE,MAAM,CACd,YAAY,CAAE,YAAY,CAC1B,cAAc,CAAE,cAAc,CAC9B,kBAAkB,CAAE,kBAAkB,CACtC,sBAAsB,CAAE,sBAAsB,CAC9C,eAAe,CAAE,eAAe,CAChC,OAAO,CAAE,OAAO,CAChB,QAAQ,CAAE,QAAQ,CAClB,UAAU,CAAE,aAAa,CACzB,YAAY,CAAE,YAAY,CAE1B;AACA,kBAAkB,CAAE,kBAAkB,CACtC,eAAe,CAAE,eAAe,CAChC,0BAA0B,CAAE,0BAA0B,CACtD,mBAAmB,CAAE,mBAAmB,CACxC,wBAAwB,CAAE,wBAAwB,CAClD,aAAa,CAAE,aAAa,CAC5B,aAAa,CAAE,aAAa,CAC5B,YAAY,CAAE,YAAY,CAC1B,gBAAgB,CAAE,gBAAgB,CAClC,kBAAkB,CAAE,kBAAkB,CACtC,mBAAmB,CAAE,gCAAgC,CAErD;AACA,cAAc,CAAE,cAAc,CAC9B,eAAe,CAAE,eAAe,CAChC,aAAa,CAAE,aAAa,CAC5B,eAAe,CAAE,eAAe,CAChC,UAAU,CAAE,UAAU,CACtB,SAAS,CAAE,SAAS,CACpB,kBAAkB,CAAE,kBAAkB,CAEtC;AACA,MAAM,CAAE,MAAM,CACd,QAAQ,CAAE,QAAQ,CAClB,MAAM,CAAE,MAAM,CACd,QAAQ,CAAE,QAAQ,CAClB,KAAK,CAAE,KAAK,CACZ,SAAS,CAAE,YAAY,CACvB,OAAO,CAAE,OAAO,CAChB,SAAS,CAAE,SAAS,CACpB,QAAQ,CAAE,QAAQ,CAClB,WAAW,CAAE,WAAW,CACxB,UAAU,CAAE,UAAU,CACtB,WAAW,CAAE,WAAW,CACxB,QAAQ,CAAE,QAAQ,CAClB,SAAS,CAAE,SACb,CAAC,CACDC,EAAE,CAAE,CACF;AACA,oBAAoB,CAAE,mBAAmB,CACzC,iBAAiB,CAAE,mBAAmB,CACtC,eAAe,CAAE,sBAAsB,CACvC,WAAW,CAAE,WAAW,CACxB,cAAc,CAAE,yBAAyB,CACzC,QAAQ,CAAE,eAAe,CAEzB;AACA,uBAAuB,CAAE,uBAAuB,CAChD,mBAAmB,CAAE,wBAAwB,CAC7C,QAAQ,CAAE,SAAS,CACnB,iBAAiB,CAAE,uBAAuB,CAC1C,OAAO,CAAE,OAAO,CAChB,YAAY,CAAE,oBAAoB,CAClC,UAAU,CAAE,UAAU,CACtB,UAAU,CAAE,UAAU,CACtB,MAAM,CAAE,QAAQ,CAChB,YAAY,CAAE,iBAAiB,CAC/B,cAAc,CAAE,sBAAsB,CACtC,kBAAkB,CAAE,sBAAsB,CAC1C,sBAAsB,CAAE,0BAA0B,CAClD,eAAe,CAAE,kBAAkB,CACnC,OAAO,CAAE,OAAO,CAChB,QAAQ,CAAE,OAAO,CACjB,UAAU,CAAE,YAAY,CACxB,YAAY,CAAE,gBAAgB,CAE9B;AACA,kBAAkB,CAAE,qBAAqB,CACzC,eAAe,CAAE,cAAc,CAC/B,0BAA0B,CAAE,iCAAiC,CAC7D,mBAAmB,CAAE,yBAAyB,CAC9C,wBAAwB,CAAE,2BAA2B,CACrD,aAAa,CAAE,cAAc,CAC7B,aAAa,CAAE,gBAAgB,CAC/B,YAAY,CAAE,aAAa,CAC3B,gBAAgB,CAAE,kBAAkB,CACpC,kBAAkB,CAAE,mBAAmB,CACvC,mBAAmB,CAAE,kCAAkC,CAEvD;AACA,cAAc,CAAE,gCAAgC,CAChD,eAAe,CAAE,gCAAgC,CACjD,aAAa,CAAE,oCAAoC,CACnD,eAAe,CAAE,qBAAqB,CACtC,UAAU,CAAE,YAAY,CACxB,SAAS,CAAE,gBAAgB,CAC3B,kBAAkB,CAAE,8BAA8B,CAElD;AACA,MAAM,CAAE,SAAS,CACjB,QAAQ,CAAE,UAAU,CACpB,MAAM,CAAE,QAAQ,CAChB,QAAQ,CAAE,UAAU,CACpB,KAAK,CAAE,SAAS,CAChB,SAAS,CAAE,aAAa,CACxB,OAAO,CAAE,OAAO,CAChB,SAAS,CAAE,OAAO,CAClB,QAAQ,CAAE,QAAQ,CAClB,WAAW,CAAE,YAAY,CACzB,UAAU,CAAE,WAAW,CACvB,WAAW,CAAE,WAAW,CACxB,QAAQ,CAAE,UAAU,CACpB,SAAS,CAAE,SACb,CACF,CAAC,CAED,MAAO,MAAM,CAAAC,gBAAgB,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CAC3C,KAAM,CAACE,QAAQ,CAAEC,WAAW,CAAC,CAAGd,QAAQ,CAAC,IAAI,CAAC,CAE9C,KAAM,CAAAe,CAAC,CAAIC,GAAG,EAAK,CACjB,MAAO,CAAAT,YAAY,CAACM,QAAQ,CAAC,CAACG,GAAG,CAAC,EAAIA,GAAG,CAC3C,CAAC,CAED,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3BH,WAAW,CAACI,IAAI,EAAIA,IAAI,GAAK,IAAI,CAAG,IAAI,CAAG,IAAI,CAAC,CAClD,CAAC,CAED,mBACEhB,IAAA,CAACC,eAAe,CAACgB,QAAQ,EAACC,KAAK,CAAE,CAAEP,QAAQ,CAAEC,WAAW,CAAEC,CAAC,CAAEE,cAAe,CAAE,CAAAL,QAAA,CAC3EA,QAAQ,CACe,CAAC,CAE/B,CAAC,CAED,cAAe,CAAAT,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}