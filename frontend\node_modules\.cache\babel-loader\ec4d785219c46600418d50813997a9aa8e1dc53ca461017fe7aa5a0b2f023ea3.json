{"ast": null, "code": "export { useAutocomplete as default, createFilterOptions } from '@mui/base';", "map": {"version": 3, "names": ["useAutocomplete", "default", "createFilterOptions"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/useAutocomplete/index.js"], "sourcesContent": ["export { useAutocomplete as default, createFilterOptions } from '@mui/base';"], "mappings": "AAAA,SAASA,eAAe,IAAIC,OAAO,EAAEC,mBAAmB,QAAQ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}