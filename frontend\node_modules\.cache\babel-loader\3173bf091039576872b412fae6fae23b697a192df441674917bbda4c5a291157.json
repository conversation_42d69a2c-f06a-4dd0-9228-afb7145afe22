{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport AvatarGroup from '@mui/material/AvatarGroup';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\n\n/**\n * @ignore - do not document.\n */\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedAvatarGroup(props, ref) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The AvatarGroup component was moved from the lab to the core.', '', \"You should use `import { AvatarGroup } from '@mui/material'`\", \"or `import AvatarGroup from '@mui/material/AvatarGroup'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return /*#__PURE__*/_jsx(AvatarGroup, _extends({\n    ref: ref\n  }, props));\n});", "map": {"version": 3, "names": ["_extends", "React", "AvatarGroup", "jsx", "_jsx", "warnedOnce", "forwardRef", "DeprecatedAvatarGroup", "props", "ref", "console", "warn", "join"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/AvatarGroup/AvatarGroup.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport AvatarGroup from '@mui/material/AvatarGroup';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\n\n/**\n * @ignore - do not document.\n */\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedAvatarGroup(props, ref) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The AvatarGroup component was moved from the lab to the core.', '', \"You should use `import { AvatarGroup } from '@mui/material'`\", \"or `import AvatarGroup from '@mui/material/AvatarGroup'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return /*#__PURE__*/_jsx(AvatarGroup, _extends({\n    ref: ref\n  }, props));\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,IAAIC,UAAU,GAAG,KAAK;;AAEtB;AACA;AACA;AACA,eAAe,aAAaJ,KAAK,CAACK,UAAU,CAAC,SAASC,qBAAqBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACtF,IAAI,CAACJ,UAAU,EAAE;IACfK,OAAO,CAACC,IAAI,CAAC,CAAC,oEAAoE,EAAE,EAAE,EAAE,8DAA8D,EAAE,0DAA0D,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/NP,UAAU,GAAG,IAAI;EACnB;EACA,OAAO,aAAaD,IAAI,CAACF,WAAW,EAAEF,QAAQ,CAAC;IAC7CS,GAAG,EAAEA;EACP,CAAC,EAAED,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}