{"ast": null, "code": "'use client';\n\nexport { Tab } from './Tab';\nexport * from './Tab.types';\nexport * from './tabClasses';", "map": {"version": 3, "names": ["Tab"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/base/Tab/index.js"], "sourcesContent": ["'use client';\n\nexport { Tab } from './Tab';\nexport * from './Tab.types';\nexport * from './tabClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,GAAG,QAAQ,OAAO;AAC3B,cAAc,aAAa;AAC3B,cAAc,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}