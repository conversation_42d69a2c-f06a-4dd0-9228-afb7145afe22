import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container, Paper, TextField, Button, Typography, Box, Alert,
  CircularProgress, ToggleButton, ToggleButtonGroup, IconButton,
  Divider, Card, CardContent
} from '@mui/material';
import {
  LocalShipping, AdminPanelSettings, Engineering,
  Language, Visibility, VisibilityOff
} from '@mui/icons-material';
import { useLanguage } from '../../contexts/LanguageContext';
import axios from 'axios';

const ModernLogin = () => {
  const [role, setRole] = useState('driver');
  const [credentials, setCredentials] = useState({ email: '', password: '' });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();
  const { t, toggleLanguage, language } = useLanguage();

  const roles = [
    { 
      value: 'driver', 
      label: t('driver_login'), 
      icon: <LocalShipping />, 
      color: '#10a37f',
      route: '/driver/dashboard',
      demoEmail: '<EMAIL>',
      demoPassword: 'driver123'
    },
    { 
      value: 'trucker', 
      label: t('trucker_login'), 
      icon: <Engineering />, 
      color: '#6366f1',
      route: '/trucker/dashboard',
      demoEmail: '<EMAIL>',
      demoPassword: 'trucker123'
    },
    { 
      value: 'admin', 
      label: t('admin_login'), 
      icon: <AdminPanelSettings />, 
      color: '#dc2626',
      route: '/dispatch',
      demoEmail: '<EMAIL>',
      demoPassword: 'admin123'
    }
  ];

  const currentRole = roles.find(r => r.value === role);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await axios.post('/api/auth/login', {
        ...credentials,
        role
      });

      // Store token and user info
      localStorage.setItem(`${role}_token`, response.data.token);
      localStorage.setItem(`${role}_user`, JSON.stringify(response.data.user));

      // Navigate to appropriate dashboard
      navigate(currentRole.route);
    } catch (err) {
      setError(err.response?.data?.error || t('error'));
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    setCredentials({
      ...credentials,
      [e.target.name]: e.target.value
    });
  };

  const handleRoleChange = (event, newRole) => {
    if (newRole !== null) {
      setRole(newRole);
      setCredentials({ email: '', password: '' });
      setError('');
    }
  };

  const fillDemoCredentials = () => {
    setCredentials({
      email: currentRole.demoEmail,
      password: currentRole.demoPassword
    });
  };

  return (
    <Container component="main" maxWidth="sm">
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          py: 4,
        }}
      >
        {/* Language Toggle */}
        <Box sx={{ position: 'absolute', top: 20, right: 20 }}>
          <IconButton onClick={toggleLanguage} color="primary">
            <Language />
            <Typography variant="caption" sx={{ ml: 1 }}>
              {language.toUpperCase()}
            </Typography>
          </IconButton>
        </Box>

        {/* Logo/Title */}
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Typography variant="h3" sx={{ fontWeight: 700, color: '#2d3748', mb: 1 }}>
            Materials Pro
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Construction Materials Management System
          </Typography>
        </Box>

        <Paper 
          elevation={3} 
          sx={{ 
            p: 4, 
            borderRadius: 3,
            background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
            border: '1px solid #e2e8f0'
          }}
        >
          {/* Role Selection */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" sx={{ mb: 2, textAlign: 'center', fontWeight: 600 }}>
              Select Your Role
            </Typography>
            <ToggleButtonGroup
              value={role}
              exclusive
              onChange={handleRoleChange}
              sx={{ 
                width: '100%',
                '& .MuiToggleButton-root': {
                  flex: 1,
                  py: 2,
                  borderRadius: 2,
                  border: '2px solid #e2e8f0',
                  '&.Mui-selected': {
                    borderColor: currentRole?.color,
                    backgroundColor: `${currentRole?.color}15`,
                    color: currentRole?.color,
                  }
                }
              }}
            >
              {roles.map((roleOption) => (
                <ToggleButton key={roleOption.value} value={roleOption.value}>
                  <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                    {roleOption.icon}
                    <Typography variant="caption" sx={{ fontWeight: 500 }}>
                      {roleOption.label.replace(' Login', '')}
                    </Typography>
                  </Box>
                </ToggleButton>
              ))}
            </ToggleButtonGroup>
          </Box>

          <Divider sx={{ my: 3 }} />

          {/* Login Form */}
          <Box sx={{ textAlign: 'center', mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
              {currentRole?.icon}
              <Typography variant="h5" sx={{ ml: 1, fontWeight: 600, color: currentRole?.color }}>
                {currentRole?.label}
              </Typography>
            </Box>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 3, borderRadius: 2 }}>
              {error}
            </Alert>
          )}

          <form onSubmit={handleSubmit}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="email"
              label={t('email_address')}
              name="email"
              autoComplete="email"
              autoFocus
              value={credentials.email}
              onChange={handleChange}
              sx={{ mb: 2 }}
            />
            
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label={t('password')}
              type={showPassword ? 'text' : 'password'}
              id="password"
              autoComplete="current-password"
              value={credentials.password}
              onChange={handleChange}
              InputProps={{
                endAdornment: (
                  <IconButton
                    onClick={() => setShowPassword(!showPassword)}
                    edge="end"
                  >
                    {showPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                ),
              }}
              sx={{ mb: 3 }}
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              disabled={loading}
              sx={{
                py: 1.5,
                fontSize: '1.1rem',
                fontWeight: 600,
                backgroundColor: currentRole?.color,
                '&:hover': {
                  backgroundColor: currentRole?.color,
                  filter: 'brightness(0.9)',
                },
                mb: 2
              }}
            >
              {loading ? <CircularProgress size={24} color="inherit" /> : t('sign_in')}
            </Button>
          </form>

          {/* Demo Credentials */}
          <Card 
            sx={{ 
              mt: 3, 
              backgroundColor: '#f8fafc', 
              border: '1px solid #e2e8f0',
              cursor: 'pointer',
              transition: 'all 0.2s',
              '&:hover': {
                backgroundColor: '#f1f5f9',
                transform: 'translateY(-1px)',
              }
            }}
            onClick={fillDemoCredentials}
          >
            <CardContent sx={{ py: 2 }}>
              <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 600, mb: 1 }}>
                {t('demo_credentials')} - Click to fill
              </Typography>
              <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                Email: {currentRole?.demoEmail}<br />
                Password: {currentRole?.demoPassword}
              </Typography>
            </CardContent>
          </Card>
        </Paper>

        {/* Footer */}
        <Box sx={{ textAlign: 'center', mt: 4 }}>
          <Typography variant="body2" color="text.secondary">
            © 2024 Materials Pro. Built with geofencing technology.
          </Typography>
        </Box>
      </Box>
    </Container>
  );
};

export default ModernLogin;
