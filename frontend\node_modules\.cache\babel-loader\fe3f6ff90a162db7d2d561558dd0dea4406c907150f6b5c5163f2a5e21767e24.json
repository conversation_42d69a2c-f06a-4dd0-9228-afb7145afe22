{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Skeleton from '@mui/material/Skeleton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\n\n/**\n * @ignore - do not document.\n */\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedSkeleton(props, ref) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The Skeleton component was moved from the lab to the core.', '', \"You should use `import { Skeleton } from '@mui/material'`\", \"or `import Skeleton from '@mui/material/Skeleton'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return /*#__PURE__*/_jsx(Skeleton, _extends({\n    ref: ref\n  }, props));\n});", "map": {"version": 3, "names": ["_extends", "React", "Skeleton", "jsx", "_jsx", "warnedOnce", "forwardRef", "DeprecatedSkeleton", "props", "ref", "console", "warn", "join"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/Skeleton/Skeleton.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Skeleton from '@mui/material/Skeleton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\n\n/**\n * @ignore - do not document.\n */\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedSkeleton(props, ref) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The Skeleton component was moved from the lab to the core.', '', \"You should use `import { Skeleton } from '@mui/material'`\", \"or `import Skeleton from '@mui/material/Skeleton'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return /*#__PURE__*/_jsx(Skeleton, _extends({\n    ref: ref\n  }, props));\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,IAAIC,UAAU,GAAG,KAAK;;AAEtB;AACA;AACA;AACA,eAAe,aAAaJ,KAAK,CAACK,UAAU,CAAC,SAASC,kBAAkBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACnF,IAAI,CAACJ,UAAU,EAAE;IACfK,OAAO,CAACC,IAAI,CAAC,CAAC,iEAAiE,EAAE,EAAE,EAAE,2DAA2D,EAAE,oDAAoD,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACnNP,UAAU,GAAG,IAAI;EACnB;EACA,OAAO,aAAaD,IAAI,CAACF,QAAQ,EAAEF,QAAQ,CAAC;IAC1CS,GAAG,EAAEA;EACP,CAAC,EAAED,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}