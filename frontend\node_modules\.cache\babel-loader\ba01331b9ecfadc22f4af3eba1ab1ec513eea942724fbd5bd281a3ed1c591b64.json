{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"actions\", \"anchor\", \"children\", \"onItemsChange\", \"slotProps\", \"slots\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { HTMLElementType, refType } from '@mui/utils';\nimport { getMenuUtilityClass } from './menuClasses';\nimport { useMenu } from '../useMenu';\nimport { MenuProvider } from '../useMenu/MenuProvider';\nimport { unstable_composeClasses as composeClasses } from '../composeClasses';\nimport { Unstable_Popup as Popup } from '../Unstable_Popup';\nimport { useSlotProps } from '../utils/useSlotProps';\nimport { useClassNamesOverride } from '../utils/ClassNameConfigurator';\nimport { ListActionTypes } from '../useList';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction useUtilityClasses(ownerState) {\n  const {\n    open\n  } = ownerState;\n  const slots = {\n    root: ['root', open && 'expanded'],\n    listbox: ['listbox', open && 'expanded']\n  };\n  return composeClasses(slots, useClassNamesOverride(getMenuUtilityClass));\n}\n\n/**\n *\n * Demos:\n *\n * - [Menu](https://mui.com/base-ui/react-menu/)\n *\n * API:\n *\n * - [Menu API](https://mui.com/base-ui/react-menu/components-api/#menu)\n */\nconst Menu = /*#__PURE__*/React.forwardRef(function Menu(props, forwardedRef) {\n  var _slots$root, _slots$listbox;\n  const {\n      actions,\n      anchor: anchorProp,\n      children,\n      onItemsChange,\n      slotProps = {},\n      slots = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    contextValue,\n    getListboxProps,\n    dispatch,\n    open,\n    triggerElement\n  } = useMenu({\n    onItemsChange,\n    componentName: 'Menu'\n  });\n  const anchor = anchorProp != null ? anchorProp : triggerElement;\n  React.useImperativeHandle(actions, () => ({\n    dispatch,\n    resetHighlight: () => dispatch({\n      type: ListActionTypes.resetHighlight,\n      event: null\n    })\n  }), [dispatch]);\n  const ownerState = _extends({}, props, {\n    open\n  });\n  const classes = useUtilityClasses(ownerState);\n  const Root = (_slots$root = slots.root) != null ? _slots$root : 'div';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: slotProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref: forwardedRef,\n      role: undefined\n    },\n    className: classes.root,\n    ownerState\n  });\n  const Listbox = (_slots$listbox = slots.listbox) != null ? _slots$listbox : 'ul';\n  const listboxProps = useSlotProps({\n    elementType: Listbox,\n    getSlotProps: getListboxProps,\n    externalSlotProps: slotProps.listbox,\n    className: classes.listbox,\n    ownerState\n  });\n  if (open === true && anchor == null) {\n    return /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n      children: /*#__PURE__*/_jsx(Listbox, _extends({}, listboxProps, {\n        children: /*#__PURE__*/_jsx(MenuProvider, {\n          value: contextValue,\n          children: children\n        })\n      }))\n    }));\n  }\n  return /*#__PURE__*/_jsx(Popup, _extends({\n    keepMounted: true\n  }, rootProps, {\n    open: open,\n    anchor: anchor,\n    slots: {\n      root: Root\n    },\n    children: /*#__PURE__*/_jsx(Listbox, _extends({}, listboxProps, {\n      children: /*#__PURE__*/_jsx(MenuProvider, {\n        value: contextValue,\n        children: children\n      })\n    }))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Menu.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A ref with imperative actions that can be performed on the menu.\n   */\n  actions: refType,\n  /**\n   * The element based on which the menu is positioned.\n   */\n  anchor: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.object, PropTypes.func]),\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Function called when the items displayed in the menu change.\n   */\n  onItemsChange: PropTypes.func,\n  /**\n   * The props used for each slot inside the Menu.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    listbox: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Menu.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    listbox: PropTypes.elementType,\n    root: PropTypes.elementType\n  })\n} : void 0;\nexport { Menu };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "HTMLElementType", "refType", "getMenuUtilityClass", "useMenu", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unstable_composeClasses", "composeClasses", "Unstable_Popup", "Popup", "useSlotProps", "useClassNamesOverride", "ListActionTypes", "jsx", "_jsx", "useUtilityClasses", "ownerState", "open", "slots", "root", "listbox", "<PERSON><PERSON>", "forwardRef", "props", "forwardedRef", "_slots$root", "_slots$listbox", "actions", "anchor", "anchorProp", "children", "onItemsChange", "slotProps", "other", "contextValue", "getListboxProps", "dispatch", "triggerElement", "componentName", "useImperativeHandle", "resetHighlight", "type", "event", "classes", "Root", "rootProps", "elementType", "externalSlotProps", "externalForwardedProps", "additionalProps", "ref", "role", "undefined", "className", "Listbox", "listboxProps", "getSlotProps", "value", "keepMounted", "process", "env", "NODE_ENV", "propTypes", "oneOfType", "object", "func", "node", "string", "shape"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/base/Menu/Menu.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"actions\", \"anchor\", \"children\", \"onItemsChange\", \"slotProps\", \"slots\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { HTMLElementType, refType } from '@mui/utils';\nimport { getMenuUtilityClass } from './menuClasses';\nimport { useMenu } from '../useMenu';\nimport { MenuProvider } from '../useMenu/MenuProvider';\nimport { unstable_composeClasses as composeClasses } from '../composeClasses';\nimport { Unstable_Popup as Popup } from '../Unstable_Popup';\nimport { useSlotProps } from '../utils/useSlotProps';\nimport { useClassNamesOverride } from '../utils/ClassNameConfigurator';\nimport { ListActionTypes } from '../useList';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction useUtilityClasses(ownerState) {\n  const {\n    open\n  } = ownerState;\n  const slots = {\n    root: ['root', open && 'expanded'],\n    listbox: ['listbox', open && 'expanded']\n  };\n  return composeClasses(slots, useClassNamesOverride(getMenuUtilityClass));\n}\n\n/**\n *\n * Demos:\n *\n * - [Menu](https://mui.com/base-ui/react-menu/)\n *\n * API:\n *\n * - [Menu API](https://mui.com/base-ui/react-menu/components-api/#menu)\n */\nconst Menu = /*#__PURE__*/React.forwardRef(function Menu(props, forwardedRef) {\n  var _slots$root, _slots$listbox;\n  const {\n      actions,\n      anchor: anchorProp,\n      children,\n      onItemsChange,\n      slotProps = {},\n      slots = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    contextValue,\n    getListboxProps,\n    dispatch,\n    open,\n    triggerElement\n  } = useMenu({\n    onItemsChange,\n    componentName: 'Menu'\n  });\n  const anchor = anchorProp != null ? anchorProp : triggerElement;\n  React.useImperativeHandle(actions, () => ({\n    dispatch,\n    resetHighlight: () => dispatch({\n      type: ListActionTypes.resetHighlight,\n      event: null\n    })\n  }), [dispatch]);\n  const ownerState = _extends({}, props, {\n    open\n  });\n  const classes = useUtilityClasses(ownerState);\n  const Root = (_slots$root = slots.root) != null ? _slots$root : 'div';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: slotProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref: forwardedRef,\n      role: undefined\n    },\n    className: classes.root,\n    ownerState\n  });\n  const Listbox = (_slots$listbox = slots.listbox) != null ? _slots$listbox : 'ul';\n  const listboxProps = useSlotProps({\n    elementType: Listbox,\n    getSlotProps: getListboxProps,\n    externalSlotProps: slotProps.listbox,\n    className: classes.listbox,\n    ownerState\n  });\n  if (open === true && anchor == null) {\n    return /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n      children: /*#__PURE__*/_jsx(Listbox, _extends({}, listboxProps, {\n        children: /*#__PURE__*/_jsx(MenuProvider, {\n          value: contextValue,\n          children: children\n        })\n      }))\n    }));\n  }\n  return /*#__PURE__*/_jsx(Popup, _extends({\n    keepMounted: true\n  }, rootProps, {\n    open: open,\n    anchor: anchor,\n    slots: {\n      root: Root\n    },\n    children: /*#__PURE__*/_jsx(Listbox, _extends({}, listboxProps, {\n      children: /*#__PURE__*/_jsx(MenuProvider, {\n        value: contextValue,\n        children: children\n      })\n    }))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Menu.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A ref with imperative actions that can be performed on the menu.\n   */\n  actions: refType,\n  /**\n   * The element based on which the menu is positioned.\n   */\n  anchor: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.object, PropTypes.func]),\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Function called when the items displayed in the menu change.\n   */\n  onItemsChange: PropTypes.func,\n  /**\n   * The props used for each slot inside the Menu.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    listbox: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Menu.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    listbox: PropTypes.elementType,\n    root: PropTypes.elementType\n  })\n} : void 0;\nexport { Menu };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,eAAe,EAAE,WAAW,EAAE,OAAO,CAAC;AAC1F,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,eAAe,EAAEC,OAAO,QAAQ,YAAY;AACrD,SAASC,mBAAmB,QAAQ,eAAe;AACnD,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,mBAAmB;AAC7E,SAASC,cAAc,IAAIC,KAAK,QAAQ,mBAAmB;AAC3D,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,eAAe,QAAQ,YAAY;AAC5C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,iBAAiBA,CAACC,UAAU,EAAE;EACrC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,IAAI,IAAI,UAAU,CAAC;IAClCG,OAAO,EAAE,CAAC,SAAS,EAAEH,IAAI,IAAI,UAAU;EACzC,CAAC;EACD,OAAOV,cAAc,CAACW,KAAK,EAAEP,qBAAqB,CAACR,mBAAmB,CAAC,CAAC;AAC1E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkB,IAAI,GAAG,aAAatB,KAAK,CAACuB,UAAU,CAAC,SAASD,IAAIA,CAACE,KAAK,EAAEC,YAAY,EAAE;EAC5E,IAAIC,WAAW,EAAEC,cAAc;EAC/B,MAAM;MACFC,OAAO;MACPC,MAAM,EAAEC,UAAU;MAClBC,QAAQ;MACRC,aAAa;MACbC,SAAS,GAAG,CAAC,CAAC;MACdd,KAAK,GAAG,CAAC;IACX,CAAC,GAAGK,KAAK;IACTU,KAAK,GAAGpC,6BAA6B,CAAC0B,KAAK,EAAEzB,SAAS,CAAC;EACzD,MAAM;IACJoC,YAAY;IACZC,eAAe;IACfC,QAAQ;IACRnB,IAAI;IACJoB;EACF,CAAC,GAAGjC,OAAO,CAAC;IACV2B,aAAa;IACbO,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAMV,MAAM,GAAGC,UAAU,IAAI,IAAI,GAAGA,UAAU,GAAGQ,cAAc;EAC/DtC,KAAK,CAACwC,mBAAmB,CAACZ,OAAO,EAAE,OAAO;IACxCS,QAAQ;IACRI,cAAc,EAAEA,CAAA,KAAMJ,QAAQ,CAAC;MAC7BK,IAAI,EAAE7B,eAAe,CAAC4B,cAAc;MACpCE,KAAK,EAAE;IACT,CAAC;EACH,CAAC,CAAC,EAAE,CAACN,QAAQ,CAAC,CAAC;EACf,MAAMpB,UAAU,GAAGpB,QAAQ,CAAC,CAAC,CAAC,EAAE2B,KAAK,EAAE;IACrCN;EACF,CAAC,CAAC;EACF,MAAM0B,OAAO,GAAG5B,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM4B,IAAI,GAAG,CAACnB,WAAW,GAAGP,KAAK,CAACC,IAAI,KAAK,IAAI,GAAGM,WAAW,GAAG,KAAK;EACrE,MAAMoB,SAAS,GAAGnC,YAAY,CAAC;IAC7BoC,WAAW,EAAEF,IAAI;IACjBG,iBAAiB,EAAEf,SAAS,CAACb,IAAI;IACjC6B,sBAAsB,EAAEf,KAAK;IAC7BgB,eAAe,EAAE;MACfC,GAAG,EAAE1B,YAAY;MACjB2B,IAAI,EAAEC;IACR,CAAC;IACDC,SAAS,EAAEV,OAAO,CAACxB,IAAI;IACvBH;EACF,CAAC,CAAC;EACF,MAAMsC,OAAO,GAAG,CAAC5B,cAAc,GAAGR,KAAK,CAACE,OAAO,KAAK,IAAI,GAAGM,cAAc,GAAG,IAAI;EAChF,MAAM6B,YAAY,GAAG7C,YAAY,CAAC;IAChCoC,WAAW,EAAEQ,OAAO;IACpBE,YAAY,EAAErB,eAAe;IAC7BY,iBAAiB,EAAEf,SAAS,CAACZ,OAAO;IACpCiC,SAAS,EAAEV,OAAO,CAACvB,OAAO;IAC1BJ;EACF,CAAC,CAAC;EACF,IAAIC,IAAI,KAAK,IAAI,IAAIW,MAAM,IAAI,IAAI,EAAE;IACnC,OAAO,aAAad,IAAI,CAAC8B,IAAI,EAAEhD,QAAQ,CAAC,CAAC,CAAC,EAAEiD,SAAS,EAAE;MACrDf,QAAQ,EAAE,aAAahB,IAAI,CAACwC,OAAO,EAAE1D,QAAQ,CAAC,CAAC,CAAC,EAAE2D,YAAY,EAAE;QAC9DzB,QAAQ,EAAE,aAAahB,IAAI,CAACT,YAAY,EAAE;UACxCoD,KAAK,EAAEvB,YAAY;UACnBJ,QAAQ,EAAEA;QACZ,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;EACL;EACA,OAAO,aAAahB,IAAI,CAACL,KAAK,EAAEb,QAAQ,CAAC;IACvC8D,WAAW,EAAE;EACf,CAAC,EAAEb,SAAS,EAAE;IACZ5B,IAAI,EAAEA,IAAI;IACVW,MAAM,EAAEA,MAAM;IACdV,KAAK,EAAE;MACLC,IAAI,EAAEyB;IACR,CAAC;IACDd,QAAQ,EAAE,aAAahB,IAAI,CAACwC,OAAO,EAAE1D,QAAQ,CAAC,CAAC,CAAC,EAAE2D,YAAY,EAAE;MAC9DzB,QAAQ,EAAE,aAAahB,IAAI,CAACT,YAAY,EAAE;QACxCoD,KAAK,EAAEvB,YAAY;QACnBJ,QAAQ,EAAEA;MACZ,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF6B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxC,IAAI,CAACyC,SAAS,CAAC,yBAAyB;EAC9E;EACA;EACA;EACA;EACA;AACF;AACA;EACEnC,OAAO,EAAEzB,OAAO;EAChB;AACF;AACA;EACE0B,MAAM,EAAE5B,SAAS,CAAC,sCAAsC+D,SAAS,CAAC,CAAC9D,eAAe,EAAED,SAAS,CAACgE,MAAM,EAAEhE,SAAS,CAACiE,IAAI,CAAC,CAAC;EACtH;AACF;AACA;EACEnC,QAAQ,EAAE9B,SAAS,CAACkE,IAAI;EACxB;AACF;AACA;EACEb,SAAS,EAAErD,SAAS,CAACmE,MAAM;EAC3B;AACF;AACA;EACEpC,aAAa,EAAE/B,SAAS,CAACiE,IAAI;EAC7B;AACF;AACA;AACA;EACEjC,SAAS,EAAEhC,SAAS,CAACoE,KAAK,CAAC;IACzBhD,OAAO,EAAEpB,SAAS,CAAC+D,SAAS,CAAC,CAAC/D,SAAS,CAACiE,IAAI,EAAEjE,SAAS,CAACgE,MAAM,CAAC,CAAC;IAChE7C,IAAI,EAAEnB,SAAS,CAAC+D,SAAS,CAAC,CAAC/D,SAAS,CAACiE,IAAI,EAAEjE,SAAS,CAACgE,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACE9C,KAAK,EAAElB,SAAS,CAACoE,KAAK,CAAC;IACrBhD,OAAO,EAAEpB,SAAS,CAAC8C,WAAW;IAC9B3B,IAAI,EAAEnB,SAAS,CAAC8C;EAClB,CAAC;AACH,CAAC,GAAG,KAAK,CAAC;AACV,SAASzB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}