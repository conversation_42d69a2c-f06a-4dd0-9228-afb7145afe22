{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"slotProps\", \"slots\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '../composeClasses';\nimport { useSlotProps } from '../utils';\nimport { getTabsListUtilityClass } from './tabsListClasses';\nimport { useTabsList } from '../useTabsList';\nimport { useClassNamesOverride } from '../utils/ClassNameConfigurator';\nimport { TabsListProvider } from '../useTabsList/TabsListProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    orientation\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation]\n  };\n  return composeClasses(slots, useClassNamesOverride(getTabsListUtilityClass));\n};\n\n/**\n *\n * Demos:\n *\n * - [Tabs](https://mui.com/base-ui/react-tabs/)\n *\n * API:\n *\n * - [TabsList API](https://mui.com/base-ui/react-tabs/components-api/#tabs-list)\n */\nconst TabsList = /*#__PURE__*/React.forwardRef(function TabsList(props, forwardedRef) {\n  var _slots$root;\n  const {\n      children,\n      slotProps = {},\n      slots = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    isRtl,\n    orientation,\n    getRootProps,\n    contextValue\n  } = useTabsList({\n    rootRef: forwardedRef\n  });\n  const ownerState = _extends({}, props, {\n    isRtl,\n    orientation\n  });\n  const classes = useUtilityClasses(ownerState);\n  const TabsListRoot = (_slots$root = slots.root) != null ? _slots$root : 'div';\n  const tabsListRootProps = useSlotProps({\n    elementType: TabsListRoot,\n    getSlotProps: getRootProps,\n    externalSlotProps: slotProps.root,\n    externalForwardedProps: other,\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(TabsListProvider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(TabsListRoot, _extends({}, tabsListRootProps, {\n      children: children\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TabsList.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The props used for each slot inside the TabsList.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the TabsList.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  })\n} : void 0;\nexport { TabsList };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "unstable_composeClasses", "composeClasses", "useSlotProps", "getTabsListUtilityClass", "useTabsList", "useClassNamesOverride", "TabsListProvider", "jsx", "_jsx", "useUtilityClasses", "ownerState", "orientation", "slots", "root", "TabsList", "forwardRef", "props", "forwardedRef", "_slots$root", "children", "slotProps", "other", "isRtl", "getRootProps", "contextValue", "rootRef", "classes", "TabsListRoot", "tabsListRootProps", "elementType", "getSlotProps", "externalSlotProps", "externalForwardedProps", "className", "value", "process", "env", "NODE_ENV", "propTypes", "node", "string", "shape", "oneOfType", "func", "object"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/base/TabsList/TabsList.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"slotProps\", \"slots\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '../composeClasses';\nimport { useSlotProps } from '../utils';\nimport { getTabsListUtilityClass } from './tabsListClasses';\nimport { useTabsList } from '../useTabsList';\nimport { useClassNamesOverride } from '../utils/ClassNameConfigurator';\nimport { TabsListProvider } from '../useTabsList/TabsListProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    orientation\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation]\n  };\n  return composeClasses(slots, useClassNamesOverride(getTabsListUtilityClass));\n};\n\n/**\n *\n * Demos:\n *\n * - [Tabs](https://mui.com/base-ui/react-tabs/)\n *\n * API:\n *\n * - [TabsList API](https://mui.com/base-ui/react-tabs/components-api/#tabs-list)\n */\nconst TabsList = /*#__PURE__*/React.forwardRef(function TabsList(props, forwardedRef) {\n  var _slots$root;\n  const {\n      children,\n      slotProps = {},\n      slots = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    isRtl,\n    orientation,\n    getRootProps,\n    contextValue\n  } = useTabsList({\n    rootRef: forwardedRef\n  });\n  const ownerState = _extends({}, props, {\n    isRtl,\n    orientation\n  });\n  const classes = useUtilityClasses(ownerState);\n  const TabsListRoot = (_slots$root = slots.root) != null ? _slots$root : 'div';\n  const tabsListRootProps = useSlotProps({\n    elementType: TabsListRoot,\n    getSlotProps: getRootProps,\n    externalSlotProps: slotProps.root,\n    externalForwardedProps: other,\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(TabsListProvider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(TabsListRoot, _extends({}, tabsListRootProps, {\n      children: children\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TabsList.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The props used for each slot inside the TabsList.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the TabsList.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  })\n} : void 0;\nexport { TabsList };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC;AACpD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,mBAAmB;AAC7E,SAASC,YAAY,QAAQ,UAAU;AACvC,SAASC,uBAAuB,QAAQ,mBAAmB;AAC3D,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,WAAW;EAC5B,CAAC;EACD,OAAOV,cAAc,CAACW,KAAK,EAAEP,qBAAqB,CAACF,uBAAuB,CAAC,CAAC;AAC9E,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMW,QAAQ,GAAG,aAAahB,KAAK,CAACiB,UAAU,CAAC,SAASD,QAAQA,CAACE,KAAK,EAAEC,YAAY,EAAE;EACpF,IAAIC,WAAW;EACf,MAAM;MACFC,QAAQ;MACRC,SAAS,GAAG,CAAC,CAAC;MACdR,KAAK,GAAG,CAAC;IACX,CAAC,GAAGI,KAAK;IACTK,KAAK,GAAGzB,6BAA6B,CAACoB,KAAK,EAAEnB,SAAS,CAAC;EACzD,MAAM;IACJyB,KAAK;IACLX,WAAW;IACXY,YAAY;IACZC;EACF,CAAC,GAAGpB,WAAW,CAAC;IACdqB,OAAO,EAAER;EACX,CAAC,CAAC;EACF,MAAMP,UAAU,GAAGf,QAAQ,CAAC,CAAC,CAAC,EAAEqB,KAAK,EAAE;IACrCM,KAAK;IACLX;EACF,CAAC,CAAC;EACF,MAAMe,OAAO,GAAGjB,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMiB,YAAY,GAAG,CAACT,WAAW,GAAGN,KAAK,CAACC,IAAI,KAAK,IAAI,GAAGK,WAAW,GAAG,KAAK;EAC7E,MAAMU,iBAAiB,GAAG1B,YAAY,CAAC;IACrC2B,WAAW,EAAEF,YAAY;IACzBG,YAAY,EAAEP,YAAY;IAC1BQ,iBAAiB,EAAEX,SAAS,CAACP,IAAI;IACjCmB,sBAAsB,EAAEX,KAAK;IAC7BX,UAAU;IACVuB,SAAS,EAAEP,OAAO,CAACb;EACrB,CAAC,CAAC;EACF,OAAO,aAAaL,IAAI,CAACF,gBAAgB,EAAE;IACzC4B,KAAK,EAAEV,YAAY;IACnBL,QAAQ,EAAE,aAAaX,IAAI,CAACmB,YAAY,EAAEhC,QAAQ,CAAC,CAAC,CAAC,EAAEiC,iBAAiB,EAAE;MACxET,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFgB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGvB,QAAQ,CAACwB,SAAS,CAAC,yBAAyB;EAClF;EACA;EACA;EACA;EACA;AACF;AACA;EACEnB,QAAQ,EAAEpB,SAAS,CAACwC,IAAI;EACxB;AACF;AACA;EACEN,SAAS,EAAElC,SAAS,CAACyC,MAAM;EAC3B;AACF;AACA;AACA;EACEpB,SAAS,EAAErB,SAAS,CAAC0C,KAAK,CAAC;IACzB5B,IAAI,EAAEd,SAAS,CAAC2C,SAAS,CAAC,CAAC3C,SAAS,CAAC4C,IAAI,EAAE5C,SAAS,CAAC6C,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACEhC,KAAK,EAAEb,SAAS,CAAC0C,KAAK,CAAC;IACrB5B,IAAI,EAAEd,SAAS,CAAC8B;EAClB,CAAC;AACH,CAAC,GAAG,KAAK,CAAC;AACV,SAASf,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}