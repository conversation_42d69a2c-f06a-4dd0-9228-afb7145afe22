{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport SpeedDial from '@mui/material/SpeedDial';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\n\n/**\n * @ignore - do not document.\n */\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedSpeedDial(props, ref) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The SpeedDial component was moved from the lab to the core.', '', \"You should use `import { SpeedDial } from '@mui/material'`\", \"or `import SpeedDial from '@mui/material/SpeedDial'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return /*#__PURE__*/_jsx(SpeedDial, _extends({\n    ref: ref\n  }, props));\n});", "map": {"version": 3, "names": ["_extends", "React", "SpeedDial", "jsx", "_jsx", "warnedOnce", "forwardRef", "DeprecatedSpeedDial", "props", "ref", "console", "warn", "join"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/SpeedDial/SpeedDial.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport SpeedDial from '@mui/material/SpeedDial';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\n\n/**\n * @ignore - do not document.\n */\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedSpeedDial(props, ref) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The SpeedDial component was moved from the lab to the core.', '', \"You should use `import { SpeedDial } from '@mui/material'`\", \"or `import SpeedDial from '@mui/material/SpeedDial'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return /*#__PURE__*/_jsx(SpeedDial, _extends({\n    ref: ref\n  }, props));\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,IAAIC,UAAU,GAAG,KAAK;;AAEtB;AACA;AACA;AACA,eAAe,aAAaJ,KAAK,CAACK,UAAU,CAAC,SAASC,mBAAmBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACpF,IAAI,CAACJ,UAAU,EAAE;IACfK,OAAO,CAACC,IAAI,CAAC,CAAC,kEAAkE,EAAE,EAAE,EAAE,4DAA4D,EAAE,sDAAsD,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvNP,UAAU,GAAG,IAAI;EACnB;EACA,OAAO,aAAaD,IAAI,CAACF,SAAS,EAAEF,QAAQ,CAAC;IAC3CS,GAAG,EAAEA;EACP,CAAC,EAAED,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}