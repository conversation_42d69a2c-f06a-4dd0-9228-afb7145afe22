import React, { createContext, useContext, useState } from 'react';

const LanguageContext = createContext();

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

const translations = {
  en: {
    // Navigation
    'dispatch_dashboard': 'Dispatch Dashboard',
    'purchase_orders': 'Purchase Orders',
    'driver_portal': 'Driver Portal',
    'geofences': 'Geofences',
    'admin_portal': 'Admin Portal',
    'logout': 'Logout',
    
    // Purchase Orders
    'create_purchase_order': 'Create Purchase Order',
    'order_information': 'Order Information',
    'client': 'Client',
    'pickup_location': 'Pickup Location',
    'notes': 'Notes',
    'line_items': 'Line Items',
    'material': 'Material',
    'quantity': 'Quantity',
    'unit': 'Unit',
    'unit_price': 'Unit Price',
    'hauling_rate': 'Hauling Rate',
    'dropoff_location': 'Dropoff Location',
    'special_instructions': 'Special Instructions',
    'add_line_item': 'Add Line Item',
    'total': 'Total',
    'create': 'Create',
    'creating': 'Creating...',
    'line_total': 'Line Total',
    
    // Driver Portal
    'driver_dashboard': 'Driver Dashboard',
    'my_deliveries': 'My Deliveries',
    'location_tracking_active': 'Location Tracking Active',
    'location_disabled': 'Location Disabled',
    'no_deliveries_assigned': 'No deliveries assigned',
    'start_route': 'Start Route',
    'mark_loaded': 'Mark Loaded',
    'in_transit': 'In Transit',
    'mark_delivered': 'Mark Delivered',
    'confirm_delivery': 'Confirm Delivery',
    'location_required': 'Location required for delivery',
    
    // Login
    'driver_login': 'Driver Login',
    'trucker_login': 'Trucker Login',
    'admin_login': 'Admin Login',
    'email_address': 'Email Address',
    'password': 'Password',
    'sign_in': 'Sign In',
    'demo_credentials': 'Demo Credentials',
    
    // Common
    'save': 'Save',
    'cancel': 'Cancel',
    'edit': 'Edit',
    'delete': 'Delete',
    'add': 'Add',
    'loading': 'Loading...',
    'error': 'Error',
    'success': 'Success',
    'status': 'Status',
    'scheduled': 'Scheduled',
    'en_route': 'En Route',
    'delivered': 'Delivered',
    'pickup': 'Pickup',
    'dropoff': 'Dropoff'
  },
  es: {
    // Navigation
    'dispatch_dashboard': 'Panel de Despacho',
    'purchase_orders': 'Órdenes de Compra',
    'driver_portal': 'Portal del Conductor',
    'geofences': 'Geocercas',
    'admin_portal': 'Portal de Administrador',
    'logout': 'Cerrar Sesión',
    
    // Purchase Orders
    'create_purchase_order': 'Crear Orden de Compra',
    'order_information': 'Información del Pedido',
    'client': 'Cliente',
    'pickup_location': 'Ubicación de Recogida',
    'notes': 'Notas',
    'line_items': 'Artículos de Línea',
    'material': 'Material',
    'quantity': 'Cantidad',
    'unit': 'Unidad',
    'unit_price': 'Precio Unitario',
    'hauling_rate': 'Tarifa de Transporte',
    'dropoff_location': 'Ubicación de Entrega',
    'special_instructions': 'Instrucciones Especiales',
    'add_line_item': 'Agregar Artículo',
    'total': 'Total',
    'create': 'Crear',
    'creating': 'Creando...',
    'line_total': 'Total de Línea',
    
    // Driver Portal
    'driver_dashboard': 'Panel del Conductor',
    'my_deliveries': 'Mis Entregas',
    'location_tracking_active': 'Seguimiento de Ubicación Activo',
    'location_disabled': 'Ubicación Deshabilitada',
    'no_deliveries_assigned': 'No hay entregas asignadas',
    'start_route': 'Iniciar Ruta',
    'mark_loaded': 'Marcar Cargado',
    'in_transit': 'En Tránsito',
    'mark_delivered': 'Marcar Entregado',
    'confirm_delivery': 'Confirmar Entrega',
    'location_required': 'Ubicación requerida para entrega',
    
    // Login
    'driver_login': 'Inicio de Sesión del Conductor',
    'trucker_login': 'Inicio de Sesión del Camionero',
    'admin_login': 'Inicio de Sesión del Administrador',
    'email_address': 'Dirección de Correo',
    'password': 'Contraseña',
    'sign_in': 'Iniciar Sesión',
    'demo_credentials': 'Credenciales de Demostración',
    
    // Common
    'save': 'Guardar',
    'cancel': 'Cancelar',
    'edit': 'Editar',
    'delete': 'Eliminar',
    'add': 'Agregar',
    'loading': 'Cargando...',
    'error': 'Error',
    'success': 'Éxito',
    'status': 'Estado',
    'scheduled': 'Programado',
    'en_route': 'En Camino',
    'delivered': 'Entregado',
    'pickup': 'Recogida',
    'dropoff': 'Entrega'
  }
};

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState('en');

  const t = (key) => {
    return translations[language][key] || key;
  };

  const toggleLanguage = () => {
    setLanguage(prev => prev === 'en' ? 'es' : 'en');
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t, toggleLanguage }}>
      {children}
    </LanguageContext.Provider>
  );
};

export default LanguageContext;
