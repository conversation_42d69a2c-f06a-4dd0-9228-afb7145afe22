{"ast": null, "code": "'use client';\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The DesktopDateTimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { DesktopDateTimePicker } from '@mui/x-date-pickers'`\", \"or `import { DesktopDateTimePicker } from '@mui/x-date-pickers/DesktopDateTimePicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The DesktopDateTimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst DesktopDateTimePicker = /*#__PURE__*/React.forwardRef(function DeprecatedDesktopDateTimePicker() {\n  warn();\n  return null;\n});\nexport default DesktopDateTimePicker;", "map": {"version": 3, "names": ["React", "warnedOnce", "warn", "console", "join", "DesktopDateTimePicker", "forwardRef", "DeprecatedDesktopDateTimePicker"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/DesktopDateTimePicker/DesktopDateTimePicker.js"], "sourcesContent": ["'use client';\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The DesktopDateTimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { DesktopDateTimePicker } from '@mui/x-date-pickers'`\", \"or `import { DesktopDateTimePicker } from '@mui/x-date-pickers/DesktopDateTimePicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The DesktopDateTimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst DesktopDateTimePicker = /*#__PURE__*/React.forwardRef(function DeprecatedDesktopDateTimePicker() {\n  warn();\n  return null;\n});\nexport default DesktopDateTimePicker;"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,UAAU,GAAG,KAAK;AACtB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,IAAI,CAACD,UAAU,EAAE;IACfE,OAAO,CAACD,IAAI,CAAC,CAAC,8FAA8F,EAAE,EAAE,EAAE,8EAA8E,EAAE,wFAAwF,EAAE,EAAE,EAAE,qGAAqG,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;IAClZH,UAAU,GAAG,IAAI;EACnB;AACF,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMI,qBAAqB,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,SAASC,+BAA+BA,CAAA,EAAG;EACrGL,IAAI,CAAC,CAAC;EACN,OAAO,IAAI;AACb,CAAC,CAAC;AACF,eAAeG,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}