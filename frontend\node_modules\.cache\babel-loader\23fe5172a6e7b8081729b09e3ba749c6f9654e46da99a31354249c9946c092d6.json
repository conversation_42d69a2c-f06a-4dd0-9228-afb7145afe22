{"ast": null, "code": "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getTimelineSeparatorUtilityClass(slot) {\n  return generateUtilityClass('MuiTimelineSeparator', slot);\n}\nconst timelineSeparatorClasses = generateUtilityClasses('MuiTimelineSeparator', ['root']);\nexport default timelineSeparatorClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTimelineSeparatorUtilityClass", "slot", "timelineSeparatorClasses"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/TimelineSeparator/timelineSeparatorClasses.js"], "sourcesContent": ["import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getTimelineSeparatorUtilityClass(slot) {\n  return generateUtilityClass('MuiTimelineSeparator', slot);\n}\nconst timelineSeparatorClasses = generateUtilityClasses('MuiTimelineSeparator', ['root']);\nexport default timelineSeparatorClasses;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,sBAAsB,MAAM,mCAAmC;AACtE,OAAO,SAASC,gCAAgCA,CAACC,IAAI,EAAE;EACrD,OAAOH,oBAAoB,CAAC,sBAAsB,EAAEG,IAAI,CAAC;AAC3D;AACA,MAAMC,wBAAwB,GAAGH,sBAAsB,CAAC,sBAAsB,EAAE,CAAC,MAAM,CAAC,CAAC;AACzF,eAAeG,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}