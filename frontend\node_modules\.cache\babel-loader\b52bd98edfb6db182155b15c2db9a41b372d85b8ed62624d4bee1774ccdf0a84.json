{"ast": null, "code": "export { default } from './TimelineSeparator';\nexport { default as timelineSeparatorClasses } from './timelineSeparatorClasses';\nexport * from './timelineSeparatorClasses';", "map": {"version": 3, "names": ["default", "timelineSeparatorClasses"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/TimelineSeparator/index.js"], "sourcesContent": ["export { default } from './TimelineSeparator';\nexport { default as timelineSeparatorClasses } from './timelineSeparatorClasses';\nexport * from './timelineSeparatorClasses';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,qBAAqB;AAC7C,SAASA,OAAO,IAAIC,wBAAwB,QAAQ,4BAA4B;AAChF,cAAc,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}