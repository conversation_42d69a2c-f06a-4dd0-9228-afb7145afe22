{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PaginationItem from '@mui/material/PaginationItem';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\n\n/**\n * @ignore - do not document.\n */\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedPaginationItem(props, ref) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The PaginationItem component was moved from the lab to the core.', '', \"You should use `import { PaginationItem } from '@mui/material'`\", \"or `import PaginationItem from '@mui/material/PaginationItem'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return /*#__PURE__*/_jsx(PaginationItem, _extends({\n    ref: ref\n  }, props));\n});", "map": {"version": 3, "names": ["_extends", "React", "PaginationItem", "jsx", "_jsx", "warnedOnce", "forwardRef", "DeprecatedPaginationItem", "props", "ref", "console", "warn", "join"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/PaginationItem/PaginationItem.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PaginationItem from '@mui/material/PaginationItem';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\n\n/**\n * @ignore - do not document.\n */\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedPaginationItem(props, ref) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The PaginationItem component was moved from the lab to the core.', '', \"You should use `import { PaginationItem } from '@mui/material'`\", \"or `import PaginationItem from '@mui/material/PaginationItem'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return /*#__PURE__*/_jsx(PaginationItem, _extends({\n    ref: ref\n  }, props));\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,8BAA8B;AACzD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,IAAIC,UAAU,GAAG,KAAK;;AAEtB;AACA;AACA;AACA,eAAe,aAAaJ,KAAK,CAACK,UAAU,CAAC,SAASC,wBAAwBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACzF,IAAI,CAACJ,UAAU,EAAE;IACfK,OAAO,CAACC,IAAI,CAAC,CAAC,uEAAuE,EAAE,EAAE,EAAE,iEAAiE,EAAE,gEAAgE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3OP,UAAU,GAAG,IAAI;EACnB;EACA,OAAO,aAAaD,IAAI,CAACF,cAAc,EAAEF,QAAQ,CAAC;IAChDS,GAAG,EAAEA;EACP,CAAC,EAAED,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}