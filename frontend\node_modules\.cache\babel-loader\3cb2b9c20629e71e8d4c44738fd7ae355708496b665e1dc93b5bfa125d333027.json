{"ast": null, "code": "import React from'react';import{Outlet,useNavigate,useLocation}from'react-router-dom';import{AppBar,Toolbar,Typography,IconButton,Box,Drawer,List,ListItem,ListItemIcon,ListItemText,useTheme,useMediaQuery}from'@mui/material';import{Menu as MenuIcon,Dashboard,LocationOn,Receipt,Language}from'@mui/icons-material';import{useLanguage}from'../../contexts/LanguageContext';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AdminLayout=()=>{const[mobileOpen,setMobileOpen]=React.useState(false);const theme=useTheme();const isMobile=useMediaQuery(theme.breakpoints.down('md'));const navigate=useNavigate();const location=useLocation();const{t,toggleLanguage,language}=useLanguage();const handleDrawerToggle=()=>{setMobileOpen(!mobileOpen);};const menuItems=[{text:t('dispatch_dashboard'),icon:/*#__PURE__*/_jsx(Dashboard,{}),path:'/dispatch'},{text:t('purchase_orders'),icon:/*#__PURE__*/_jsx(Receipt,{}),path:'/purchase-orders'},{text:t('geofences'),icon:/*#__PURE__*/_jsx(LocationOn,{}),path:'/geofences'}];const drawer=/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Toolbar,{sx:{borderBottom:'1px solid #e5e7eb'},children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",noWrap:true,component:\"div\",sx:{fontWeight:700,color:'primary.main'},children:t('admin_portal')})}),/*#__PURE__*/_jsx(List,{sx:{px:2,py:3},children:menuItems.map(item=>/*#__PURE__*/_jsxs(ListItem,{button:true,selected:location.pathname===item.path,onClick:()=>{navigate(item.path);if(isMobile)setMobileOpen(false);},sx:{mb:1},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{color:location.pathname===item.path?'inherit':'text.secondary'},children:item.icon}),/*#__PURE__*/_jsx(ListItemText,{primary:item.text,primaryTypographyProps:{fontWeight:location.pathname===item.path?600:400}})]},item.text))})]});return/*#__PURE__*/_jsxs(Box,{sx:{display:'flex'},children:[/*#__PURE__*/_jsx(AppBar,{position:\"fixed\",sx:{width:{md:\"calc(100% - 240px)\"},ml:{md:'240px'}},children:/*#__PURE__*/_jsxs(Toolbar,{children:[/*#__PURE__*/_jsx(IconButton,{color:\"inherit\",\"aria-label\":\"open drawer\",edge:\"start\",onClick:handleDrawerToggle,sx:{mr:2,display:{md:'none'}},children:/*#__PURE__*/_jsx(MenuIcon,{})}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",noWrap:true,component:\"div\",sx:{flexGrow:1},children:\"Materials Pro - Admin\"}),/*#__PURE__*/_jsxs(IconButton,{onClick:toggleLanguage,color:\"inherit\",sx:{mr:1},children:[/*#__PURE__*/_jsx(Language,{}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{ml:1,fontWeight:600},children:language.toUpperCase()})]})]})}),/*#__PURE__*/_jsxs(Box,{component:\"nav\",sx:{width:{md:240},flexShrink:{md:0}},children:[/*#__PURE__*/_jsx(Drawer,{variant:\"temporary\",open:mobileOpen,onClose:handleDrawerToggle,ModalProps:{keepMounted:true},sx:{display:{xs:'block',md:'none'},'& .MuiDrawer-paper':{boxSizing:'border-box',width:240}},children:drawer}),/*#__PURE__*/_jsx(Drawer,{variant:\"permanent\",sx:{display:{xs:'none',md:'block'},'& .MuiDrawer-paper':{boxSizing:'border-box',width:240}},open:true,children:drawer})]}),/*#__PURE__*/_jsx(Box,{component:\"main\",sx:{flexGrow:1,p:3,width:{md:\"calc(100% - 240px)\"},mt:'64px'},children:/*#__PURE__*/_jsx(Outlet,{})})]});};export default AdminLayout;", "map": {"version": 3, "names": ["React", "Outlet", "useNavigate", "useLocation", "AppBar", "<PERSON><PERSON><PERSON>", "Typography", "IconButton", "Box", "Drawer", "List", "ListItem", "ListItemIcon", "ListItemText", "useTheme", "useMediaQuery", "<PERSON><PERSON>", "MenuIcon", "Dashboard", "LocationOn", "Receipt", "Language", "useLanguage", "jsx", "_jsx", "jsxs", "_jsxs", "AdminLayout", "mobileOpen", "setMobileOpen", "useState", "theme", "isMobile", "breakpoints", "down", "navigate", "location", "t", "toggleLanguage", "language", "handleDrawerToggle", "menuItems", "text", "icon", "path", "drawer", "children", "sx", "borderBottom", "variant", "noWrap", "component", "fontWeight", "color", "px", "py", "map", "item", "button", "selected", "pathname", "onClick", "mb", "primary", "primaryTypographyProps", "display", "position", "width", "md", "ml", "edge", "mr", "flexGrow", "toUpperCase", "flexShrink", "open", "onClose", "ModalProps", "keepMounted", "xs", "boxSizing", "p", "mt"], "sources": ["C:/NewSiteKevin/frontend/src/components/layout/AdminLayout.jsx"], "sourcesContent": ["import React from 'react';\nimport { Outlet, useNavigate, useLocation } from 'react-router-dom';\nimport {\n  AppBar, Toolbar, Typography, IconButton, Box,\n  Drawer, List, ListItem, ListItemIcon, ListItemText,\n  useTheme, useMediaQuery\n} from '@mui/material';\nimport {\n  Menu as MenuIcon, Dashboard, LocationOn, Receipt, Language\n} from '@mui/icons-material';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst AdminLayout = () => {\n  const [mobileOpen, setMobileOpen] = React.useState(false);\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { t, toggleLanguage, language } = useLanguage();\n\n  const handleDrawerToggle = () => {\n    setMobileOpen(!mobileOpen);\n  };\n\n  const menuItems = [\n    { text: t('dispatch_dashboard'), icon: <Dashboard />, path: '/dispatch' },\n    { text: t('purchase_orders'), icon: <Receipt />, path: '/purchase-orders' },\n    { text: t('geofences'), icon: <LocationOn />, path: '/geofences' },\n  ];\n\n  const drawer = (\n    <Box>\n      <Toolbar sx={{ borderBottom: '1px solid #e5e7eb' }}>\n        <Typography variant=\"h6\" noWrap component=\"div\" sx={{ fontWeight: 700, color: 'primary.main' }}>\n          {t('admin_portal')}\n        </Typography>\n      </Toolbar>\n      <List sx={{ px: 2, py: 3 }}>\n        {menuItems.map((item) => (\n          <ListItem\n            button\n            key={item.text}\n            selected={location.pathname === item.path}\n            onClick={() => {\n              navigate(item.path);\n              if (isMobile) setMobileOpen(false);\n            }}\n            sx={{ mb: 1 }}\n          >\n            <ListItemIcon sx={{ color: location.pathname === item.path ? 'inherit' : 'text.secondary' }}>\n              {item.icon}\n            </ListItemIcon>\n            <ListItemText\n              primary={item.text}\n              primaryTypographyProps={{ fontWeight: location.pathname === item.path ? 600 : 400 }}\n            />\n          </ListItem>\n        ))}\n      </List>\n    </Box>\n  );\n\n  return (\n    <Box sx={{ display: 'flex' }}>\n      <AppBar\n        position=\"fixed\"\n        sx={{\n          width: { md: `calc(100% - 240px)` },\n          ml: { md: '240px' },\n        }}\n      >\n        <Toolbar>\n          <IconButton\n            color=\"inherit\"\n            aria-label=\"open drawer\"\n            edge=\"start\"\n            onClick={handleDrawerToggle}\n            sx={{ mr: 2, display: { md: 'none' } }}\n          >\n            <MenuIcon />\n          </IconButton>\n          <Typography variant=\"h6\" noWrap component=\"div\" sx={{ flexGrow: 1 }}>\n            Materials Pro - Admin\n          </Typography>\n\n          {/* Language Toggle */}\n          <IconButton onClick={toggleLanguage} color=\"inherit\" sx={{ mr: 1 }}>\n            <Language />\n            <Typography variant=\"caption\" sx={{ ml: 1, fontWeight: 600 }}>\n              {language.toUpperCase()}\n            </Typography>\n          </IconButton>\n        </Toolbar>\n      </AppBar>\n\n      <Box\n        component=\"nav\"\n        sx={{ width: { md: 240 }, flexShrink: { md: 0 } }}\n      >\n        <Drawer\n          variant=\"temporary\"\n          open={mobileOpen}\n          onClose={handleDrawerToggle}\n          ModalProps={{ keepMounted: true }}\n          sx={{\n            display: { xs: 'block', md: 'none' },\n            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: 240 },\n          }}\n        >\n          {drawer}\n        </Drawer>\n        <Drawer\n          variant=\"permanent\"\n          sx={{\n            display: { xs: 'none', md: 'block' },\n            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: 240 },\n          }}\n          open\n        >\n          {drawer}\n        </Drawer>\n      </Box>\n\n      <Box\n        component=\"main\"\n        sx={{\n          flexGrow: 1,\n          p: 3,\n          width: { md: `calc(100% - 240px)` },\n          mt: '64px'\n        }}\n      >\n        <Outlet />\n      </Box>\n    </Box>\n  );\n};\n\nexport default AdminLayout;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,CAAEC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CACnE,OACEC,MAAM,CAAEC,OAAO,CAAEC,UAAU,CAAEC,UAAU,CAAEC,GAAG,CAC5CC,MAAM,CAAEC,IAAI,CAAEC,QAAQ,CAAEC,YAAY,CAAEC,YAAY,CAClDC,QAAQ,CAAEC,aAAa,KAClB,eAAe,CACtB,OACEC,IAAI,GAAI,CAAAC,QAAQ,CAAEC,SAAS,CAAEC,UAAU,CAAEC,OAAO,CAAEC,QAAQ,KACrD,qBAAqB,CAC5B,OAASC,WAAW,KAAQ,gCAAgC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE7D,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAG7B,KAAK,CAAC8B,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAAAC,KAAK,CAAGjB,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAAkB,QAAQ,CAAGjB,aAAa,CAACgB,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAC5D,KAAM,CAAAC,QAAQ,CAAGjC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAkC,QAAQ,CAAGjC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEkC,CAAC,CAAEC,cAAc,CAAEC,QAAS,CAAC,CAAGjB,WAAW,CAAC,CAAC,CAErD,KAAM,CAAAkB,kBAAkB,CAAGA,CAAA,GAAM,CAC/BX,aAAa,CAAC,CAACD,UAAU,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAa,SAAS,CAAG,CAChB,CAAEC,IAAI,CAAEL,CAAC,CAAC,oBAAoB,CAAC,CAAEM,IAAI,cAAEnB,IAAA,CAACN,SAAS,GAAE,CAAC,CAAE0B,IAAI,CAAE,WAAY,CAAC,CACzE,CAAEF,IAAI,CAAEL,CAAC,CAAC,iBAAiB,CAAC,CAAEM,IAAI,cAAEnB,IAAA,CAACJ,OAAO,GAAE,CAAC,CAAEwB,IAAI,CAAE,kBAAmB,CAAC,CAC3E,CAAEF,IAAI,CAAEL,CAAC,CAAC,WAAW,CAAC,CAAEM,IAAI,cAAEnB,IAAA,CAACL,UAAU,GAAE,CAAC,CAAEyB,IAAI,CAAE,YAAa,CAAC,CACnE,CAED,KAAM,CAAAC,MAAM,cACVnB,KAAA,CAAClB,GAAG,EAAAsC,QAAA,eACFtB,IAAA,CAACnB,OAAO,EAAC0C,EAAE,CAAE,CAAEC,YAAY,CAAE,mBAAoB,CAAE,CAAAF,QAAA,cACjDtB,IAAA,CAAClB,UAAU,EAAC2C,OAAO,CAAC,IAAI,CAACC,MAAM,MAACC,SAAS,CAAC,KAAK,CAACJ,EAAE,CAAE,CAAEK,UAAU,CAAE,GAAG,CAAEC,KAAK,CAAE,cAAe,CAAE,CAAAP,QAAA,CAC5FT,CAAC,CAAC,cAAc,CAAC,CACR,CAAC,CACN,CAAC,cACVb,IAAA,CAACd,IAAI,EAACqC,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAT,QAAA,CACxBL,SAAS,CAACe,GAAG,CAAEC,IAAI,eAClB/B,KAAA,CAACf,QAAQ,EACP+C,MAAM,MAENC,QAAQ,CAAEvB,QAAQ,CAACwB,QAAQ,GAAKH,IAAI,CAACb,IAAK,CAC1CiB,OAAO,CAAEA,CAAA,GAAM,CACb1B,QAAQ,CAACsB,IAAI,CAACb,IAAI,CAAC,CACnB,GAAIZ,QAAQ,CAAEH,aAAa,CAAC,KAAK,CAAC,CACpC,CAAE,CACFkB,EAAE,CAAE,CAAEe,EAAE,CAAE,CAAE,CAAE,CAAAhB,QAAA,eAEdtB,IAAA,CAACZ,YAAY,EAACmC,EAAE,CAAE,CAAEM,KAAK,CAAEjB,QAAQ,CAACwB,QAAQ,GAAKH,IAAI,CAACb,IAAI,CAAG,SAAS,CAAG,gBAAiB,CAAE,CAAAE,QAAA,CACzFW,IAAI,CAACd,IAAI,CACE,CAAC,cACfnB,IAAA,CAACX,YAAY,EACXkD,OAAO,CAAEN,IAAI,CAACf,IAAK,CACnBsB,sBAAsB,CAAE,CAAEZ,UAAU,CAAEhB,QAAQ,CAACwB,QAAQ,GAAKH,IAAI,CAACb,IAAI,CAAG,GAAG,CAAG,GAAI,CAAE,CACrF,CAAC,GAdGa,IAAI,CAACf,IAeF,CACX,CAAC,CACE,CAAC,EACJ,CACN,CAED,mBACEhB,KAAA,CAAClB,GAAG,EAACuC,EAAE,CAAE,CAAEkB,OAAO,CAAE,MAAO,CAAE,CAAAnB,QAAA,eAC3BtB,IAAA,CAACpB,MAAM,EACL8D,QAAQ,CAAC,OAAO,CAChBnB,EAAE,CAAE,CACFoB,KAAK,CAAE,CAAEC,EAAE,qBAAuB,CAAC,CACnCC,EAAE,CAAE,CAAED,EAAE,CAAE,OAAQ,CACpB,CAAE,CAAAtB,QAAA,cAEFpB,KAAA,CAACrB,OAAO,EAAAyC,QAAA,eACNtB,IAAA,CAACjB,UAAU,EACT8C,KAAK,CAAC,SAAS,CACf,aAAW,aAAa,CACxBiB,IAAI,CAAC,OAAO,CACZT,OAAO,CAAErB,kBAAmB,CAC5BO,EAAE,CAAE,CAAEwB,EAAE,CAAE,CAAC,CAAEN,OAAO,CAAE,CAAEG,EAAE,CAAE,MAAO,CAAE,CAAE,CAAAtB,QAAA,cAEvCtB,IAAA,CAACP,QAAQ,GAAE,CAAC,CACF,CAAC,cACbO,IAAA,CAAClB,UAAU,EAAC2C,OAAO,CAAC,IAAI,CAACC,MAAM,MAACC,SAAS,CAAC,KAAK,CAACJ,EAAE,CAAE,CAAEyB,QAAQ,CAAE,CAAE,CAAE,CAAA1B,QAAA,CAAC,uBAErE,CAAY,CAAC,cAGbpB,KAAA,CAACnB,UAAU,EAACsD,OAAO,CAAEvB,cAAe,CAACe,KAAK,CAAC,SAAS,CAACN,EAAE,CAAE,CAAEwB,EAAE,CAAE,CAAE,CAAE,CAAAzB,QAAA,eACjEtB,IAAA,CAACH,QAAQ,GAAE,CAAC,cACZG,IAAA,CAAClB,UAAU,EAAC2C,OAAO,CAAC,SAAS,CAACF,EAAE,CAAE,CAAEsB,EAAE,CAAE,CAAC,CAAEjB,UAAU,CAAE,GAAI,CAAE,CAAAN,QAAA,CAC1DP,QAAQ,CAACkC,WAAW,CAAC,CAAC,CACb,CAAC,EACH,CAAC,EACN,CAAC,CACJ,CAAC,cAET/C,KAAA,CAAClB,GAAG,EACF2C,SAAS,CAAC,KAAK,CACfJ,EAAE,CAAE,CAAEoB,KAAK,CAAE,CAAEC,EAAE,CAAE,GAAI,CAAC,CAAEM,UAAU,CAAE,CAAEN,EAAE,CAAE,CAAE,CAAE,CAAE,CAAAtB,QAAA,eAElDtB,IAAA,CAACf,MAAM,EACLwC,OAAO,CAAC,WAAW,CACnB0B,IAAI,CAAE/C,UAAW,CACjBgD,OAAO,CAAEpC,kBAAmB,CAC5BqC,UAAU,CAAE,CAAEC,WAAW,CAAE,IAAK,CAAE,CAClC/B,EAAE,CAAE,CACFkB,OAAO,CAAE,CAAEc,EAAE,CAAE,OAAO,CAAEX,EAAE,CAAE,MAAO,CAAC,CACpC,oBAAoB,CAAE,CAAEY,SAAS,CAAE,YAAY,CAAEb,KAAK,CAAE,GAAI,CAC9D,CAAE,CAAArB,QAAA,CAEDD,MAAM,CACD,CAAC,cACTrB,IAAA,CAACf,MAAM,EACLwC,OAAO,CAAC,WAAW,CACnBF,EAAE,CAAE,CACFkB,OAAO,CAAE,CAAEc,EAAE,CAAE,MAAM,CAAEX,EAAE,CAAE,OAAQ,CAAC,CACpC,oBAAoB,CAAE,CAAEY,SAAS,CAAE,YAAY,CAAEb,KAAK,CAAE,GAAI,CAC9D,CAAE,CACFQ,IAAI,MAAA7B,QAAA,CAEHD,MAAM,CACD,CAAC,EACN,CAAC,cAENrB,IAAA,CAAChB,GAAG,EACF2C,SAAS,CAAC,MAAM,CAChBJ,EAAE,CAAE,CACFyB,QAAQ,CAAE,CAAC,CACXS,CAAC,CAAE,CAAC,CACJd,KAAK,CAAE,CAAEC,EAAE,qBAAuB,CAAC,CACnCc,EAAE,CAAE,MACN,CAAE,CAAApC,QAAA,cAEFtB,IAAA,CAACvB,MAAM,GAAE,CAAC,CACP,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA0B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}