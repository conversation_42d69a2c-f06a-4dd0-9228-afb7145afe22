import React, { useState, useEffect } from 'react';
import { 
  Grid, TextField, Button, Select, MenuItem,
  FormControl, InputLabel, Paper, Typography
} from '@mui/material';
import axios from 'axios';

const OrderForm = () => {
  const [customers, setCustomers] = useState([]);
  const [materials, setMaterials] = useState([]);
  const [order, setOrder] = useState({
    customer_id: '',
    items: [{ material_id: '', variant_id: '', quantity: '', unit_price: '' }],
    delivery: { required: false, pickup_location: '', dropoff_location: '', scheduled_date: '' }
  });

  useEffect(() => {
    const fetchData = async () => {
      const customersRes = await axios.get('/api/customers');
      const materialsRes = await axios.get('/api/materials');
      setCustomers(customersRes.data);
      setMaterials(materialsRes.data);
    };
    fetchData();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await axios.post('/api/orders', order);
      // Handle success, redirect or show confirmation
    } catch (err) {
      console.error(err);
    }
  };

  const addOrderItem = () => {
    setOrder({
      ...order,
      items: [...order.items, { material_id: '', variant_id: '', quantity: '', unit_price: '' }]
    });
  };

  return (
    <Paper style={{ padding: 20 }}>
      <Typography variant="h5" gutterBottom>Create New Order</Typography>
      
      <form onSubmit={handleSubmit}>
        <Grid container spacing={3}>
          {/* Customer selection */}
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>Customer</InputLabel>
              <Select
                value={order.customer_id}
                onChange={(e) => setOrder({...order, customer_id: e.target.value})}
              >
                {customers.map(customer => (
                  <MenuItem key={customer.id} value={customer.id}>
                    {customer.company_name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          
          {/* Order items section */}
          <Grid item xs={12}>
            <Typography variant="h6">Order Items</Typography>
            {order.items.map((item, index) => (
              <Grid container spacing={2} key={index} style={{ marginBottom: 10 }}>
                <Grid item xs={4}>
                  <FormControl fullWidth>
                    <InputLabel>Material</InputLabel>
                    <Select
                      value={item.material_id}
                      onChange={(e) => {
                        const newItems = [...order.items];
                        newItems[index].material_id = e.target.value;
                        setOrder({...order, items: newItems});
                      }}
                    >
                      {materials.map(material => (
                        <MenuItem key={material.id} value={material.id}>
                          {material.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={3}>
                  <TextField
                    fullWidth
                    label="Quantity"
                    type="number"
                    value={item.quantity}
                    onChange={(e) => {
                      const newItems = [...order.items];
                      newItems[index].quantity = e.target.value;
                      setOrder({...order, items: newItems});
                    }}
                  />
                </Grid>
                <Grid item xs={3}>
                  <TextField
                    fullWidth
                    label="Unit Price"
                    type="number"
                    value={item.unit_price}
                    onChange={(e) => {
                      const newItems = [...order.items];
                      newItems[index].unit_price = e.target.value;
                      setOrder({...order, items: newItems});
                    }}
                  />
                </Grid>
              </Grid>
            ))}
            <Button onClick={addOrderItem}>Add Item</Button>
          </Grid>
          
          {/* Delivery information */}
          {/* ... */}
          
          <Grid item xs={12}>
            <Button type="submit" variant="contained" color="primary">
              Create Order
            </Button>
          </Grid>
        </Grid>
      </form>
    </Paper>
  );
};

export default OrderForm;