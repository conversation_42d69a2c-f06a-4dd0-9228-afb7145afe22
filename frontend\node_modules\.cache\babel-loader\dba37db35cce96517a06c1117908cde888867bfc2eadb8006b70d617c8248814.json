{"ast": null, "code": "import React from'react';import{Outlet,useNavigate,useLocation}from'react-router-dom';import{AppBar,Toolbar,Typography,IconButton,Box,Drawer,List,ListItem,ListItemIcon,ListItemText,useTheme,useMediaQuery}from'@mui/material';import{Menu as MenuIcon,Dashboard,Inventory,ShoppingCart,LocationOn,Assessment,Receipt}from'@mui/icons-material';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AdminLayout=()=>{const[mobileOpen,setMobileOpen]=React.useState(false);const theme=useTheme();const isMobile=useMediaQuery(theme.breakpoints.down('md'));const navigate=useNavigate();const location=useLocation();const handleDrawerToggle=()=>{setMobileOpen(!mobileOpen);};const menuItems=[{text:'Dispatch Dashboard',icon:/*#__PURE__*/_jsx(Dashboard,{}),path:'/dispatch'},{text:'Purchase Orders',icon:/*#__PURE__*/_jsx(Receipt,{}),path:'/purchase-orders'},{text:'Materials',icon:/*#__PURE__*/_jsx(Inventory,{}),path:'/materials'},{text:'Orders',icon:/*#__PURE__*/_jsx(ShoppingCart,{}),path:'/orders'},{text:'Geofences',icon:/*#__PURE__*/_jsx(LocationOn,{}),path:'/geofences'},{text:'Reports',icon:/*#__PURE__*/_jsx(Assessment,{}),path:'/reports'}];const drawer=/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Toolbar,{children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",noWrap:true,component:\"div\",children:\"Admin Portal\"})}),/*#__PURE__*/_jsx(List,{children:menuItems.map(item=>/*#__PURE__*/_jsxs(ListItem,{button:true,selected:location.pathname===item.path,onClick:()=>{navigate(item.path);if(isMobile)setMobileOpen(false);},children:[/*#__PURE__*/_jsx(ListItemIcon,{children:item.icon}),/*#__PURE__*/_jsx(ListItemText,{primary:item.text})]},item.text))})]});return/*#__PURE__*/_jsxs(Box,{sx:{display:'flex'},children:[/*#__PURE__*/_jsx(AppBar,{position:\"fixed\",sx:{width:{md:\"calc(100% - 240px)\"},ml:{md:'240px'}},children:/*#__PURE__*/_jsxs(Toolbar,{children:[/*#__PURE__*/_jsx(IconButton,{color:\"inherit\",\"aria-label\":\"open drawer\",edge:\"start\",onClick:handleDrawerToggle,sx:{mr:2,display:{md:'none'}},children:/*#__PURE__*/_jsx(MenuIcon,{})}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",noWrap:true,component:\"div\",children:\"Construction Materials Management\"})]})}),/*#__PURE__*/_jsxs(Box,{component:\"nav\",sx:{width:{md:240},flexShrink:{md:0}},children:[/*#__PURE__*/_jsx(Drawer,{variant:\"temporary\",open:mobileOpen,onClose:handleDrawerToggle,ModalProps:{keepMounted:true},sx:{display:{xs:'block',md:'none'},'& .MuiDrawer-paper':{boxSizing:'border-box',width:240}},children:drawer}),/*#__PURE__*/_jsx(Drawer,{variant:\"permanent\",sx:{display:{xs:'none',md:'block'},'& .MuiDrawer-paper':{boxSizing:'border-box',width:240}},open:true,children:drawer})]}),/*#__PURE__*/_jsx(Box,{component:\"main\",sx:{flexGrow:1,p:3,width:{md:\"calc(100% - 240px)\"},mt:'64px'},children:/*#__PURE__*/_jsx(Outlet,{})})]});};export default AdminLayout;", "map": {"version": 3, "names": ["React", "Outlet", "useNavigate", "useLocation", "AppBar", "<PERSON><PERSON><PERSON>", "Typography", "IconButton", "Box", "Drawer", "List", "ListItem", "ListItemIcon", "ListItemText", "useTheme", "useMediaQuery", "<PERSON><PERSON>", "MenuIcon", "Dashboard", "Inventory", "ShoppingCart", "LocationOn", "Assessment", "Receipt", "jsx", "_jsx", "jsxs", "_jsxs", "AdminLayout", "mobileOpen", "setMobileOpen", "useState", "theme", "isMobile", "breakpoints", "down", "navigate", "location", "handleDrawerToggle", "menuItems", "text", "icon", "path", "drawer", "children", "variant", "noWrap", "component", "map", "item", "button", "selected", "pathname", "onClick", "primary", "sx", "display", "position", "width", "md", "ml", "color", "edge", "mr", "flexShrink", "open", "onClose", "ModalProps", "keepMounted", "xs", "boxSizing", "flexGrow", "p", "mt"], "sources": ["C:/NewSiteKevin/frontend/src/components/layout/AdminLayout.jsx"], "sourcesContent": ["import React from 'react';\nimport { Outlet, useNavigate, useLocation } from 'react-router-dom';\nimport {\n  AppBar, Toolbar, Typography, IconButton, Box,\n  Drawer, List, ListItem, ListItemIcon, ListItemText,\n  useTheme, useMediaQuery\n} from '@mui/material';\nimport {\n  Menu as MenuIcon, Dashboard, Inventory,\n  ShoppingCart, LocationOn, Assessment, Receipt\n} from '@mui/icons-material';\n\nconst AdminLayout = () => {\n  const [mobileOpen, setMobileOpen] = React.useState(false);\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  const handleDrawerToggle = () => {\n    setMobileOpen(!mobileOpen);\n  };\n\n  const menuItems = [\n    { text: 'Dispatch Dashboard', icon: <Dashboard />, path: '/dispatch' },\n    { text: 'Purchase Orders', icon: <Receipt />, path: '/purchase-orders' },\n    { text: 'Materials', icon: <Inventory />, path: '/materials' },\n    { text: 'Orders', icon: <ShoppingCart />, path: '/orders' },\n    { text: 'Geofences', icon: <LocationOn />, path: '/geofences' },\n    { text: 'Reports', icon: <Assessment />, path: '/reports' },\n  ];\n\n  const drawer = (\n    <Box>\n      <Toolbar>\n        <Typography variant=\"h6\" noWrap component=\"div\">\n          Admin Portal\n        </Typography>\n      </Toolbar>\n      <List>\n        {menuItems.map((item) => (\n          <ListItem\n            button\n            key={item.text}\n            selected={location.pathname === item.path}\n            onClick={() => {\n              navigate(item.path);\n              if (isMobile) setMobileOpen(false);\n            }}\n          >\n            <ListItemIcon>{item.icon}</ListItemIcon>\n            <ListItemText primary={item.text} />\n          </ListItem>\n        ))}\n      </List>\n    </Box>\n  );\n\n  return (\n    <Box sx={{ display: 'flex' }}>\n      <AppBar\n        position=\"fixed\"\n        sx={{\n          width: { md: `calc(100% - 240px)` },\n          ml: { md: '240px' },\n        }}\n      >\n        <Toolbar>\n          <IconButton\n            color=\"inherit\"\n            aria-label=\"open drawer\"\n            edge=\"start\"\n            onClick={handleDrawerToggle}\n            sx={{ mr: 2, display: { md: 'none' } }}\n          >\n            <MenuIcon />\n          </IconButton>\n          <Typography variant=\"h6\" noWrap component=\"div\">\n            Construction Materials Management\n          </Typography>\n        </Toolbar>\n      </AppBar>\n\n      <Box\n        component=\"nav\"\n        sx={{ width: { md: 240 }, flexShrink: { md: 0 } }}\n      >\n        <Drawer\n          variant=\"temporary\"\n          open={mobileOpen}\n          onClose={handleDrawerToggle}\n          ModalProps={{ keepMounted: true }}\n          sx={{\n            display: { xs: 'block', md: 'none' },\n            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: 240 },\n          }}\n        >\n          {drawer}\n        </Drawer>\n        <Drawer\n          variant=\"permanent\"\n          sx={{\n            display: { xs: 'none', md: 'block' },\n            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: 240 },\n          }}\n          open\n        >\n          {drawer}\n        </Drawer>\n      </Box>\n\n      <Box\n        component=\"main\"\n        sx={{\n          flexGrow: 1,\n          p: 3,\n          width: { md: `calc(100% - 240px)` },\n          mt: '64px'\n        }}\n      >\n        <Outlet />\n      </Box>\n    </Box>\n  );\n};\n\nexport default AdminLayout;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,CAAEC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CACnE,OACEC,MAAM,CAAEC,OAAO,CAAEC,UAAU,CAAEC,UAAU,CAAEC,GAAG,CAC5CC,MAAM,CAAEC,IAAI,CAAEC,QAAQ,CAAEC,YAAY,CAAEC,YAAY,CAClDC,QAAQ,CAAEC,aAAa,KAClB,eAAe,CACtB,OACEC,IAAI,GAAI,CAAAC,QAAQ,CAAEC,SAAS,CAAEC,SAAS,CACtCC,YAAY,CAAEC,UAAU,CAAEC,UAAU,CAAEC,OAAO,KACxC,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE7B,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAG9B,KAAK,CAAC+B,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAAAC,KAAK,CAAGlB,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAAmB,QAAQ,CAAGlB,aAAa,CAACiB,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAC5D,KAAM,CAAAC,QAAQ,CAAGlC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAmC,QAAQ,CAAGlC,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAmC,kBAAkB,CAAGA,CAAA,GAAM,CAC/BR,aAAa,CAAC,CAACD,UAAU,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAU,SAAS,CAAG,CAChB,CAAEC,IAAI,CAAE,oBAAoB,CAAEC,IAAI,cAAEhB,IAAA,CAACP,SAAS,GAAE,CAAC,CAAEwB,IAAI,CAAE,WAAY,CAAC,CACtE,CAAEF,IAAI,CAAE,iBAAiB,CAAEC,IAAI,cAAEhB,IAAA,CAACF,OAAO,GAAE,CAAC,CAAEmB,IAAI,CAAE,kBAAmB,CAAC,CACxE,CAAEF,IAAI,CAAE,WAAW,CAAEC,IAAI,cAAEhB,IAAA,CAACN,SAAS,GAAE,CAAC,CAAEuB,IAAI,CAAE,YAAa,CAAC,CAC9D,CAAEF,IAAI,CAAE,QAAQ,CAAEC,IAAI,cAAEhB,IAAA,CAACL,YAAY,GAAE,CAAC,CAAEsB,IAAI,CAAE,SAAU,CAAC,CAC3D,CAAEF,IAAI,CAAE,WAAW,CAAEC,IAAI,cAAEhB,IAAA,CAACJ,UAAU,GAAE,CAAC,CAAEqB,IAAI,CAAE,YAAa,CAAC,CAC/D,CAAEF,IAAI,CAAE,SAAS,CAAEC,IAAI,cAAEhB,IAAA,CAACH,UAAU,GAAE,CAAC,CAAEoB,IAAI,CAAE,UAAW,CAAC,CAC5D,CAED,KAAM,CAAAC,MAAM,cACVhB,KAAA,CAACnB,GAAG,EAAAoC,QAAA,eACFnB,IAAA,CAACpB,OAAO,EAAAuC,QAAA,cACNnB,IAAA,CAACnB,UAAU,EAACuC,OAAO,CAAC,IAAI,CAACC,MAAM,MAACC,SAAS,CAAC,KAAK,CAAAH,QAAA,CAAC,cAEhD,CAAY,CAAC,CACN,CAAC,cACVnB,IAAA,CAACf,IAAI,EAAAkC,QAAA,CACFL,SAAS,CAACS,GAAG,CAAEC,IAAI,eAClBtB,KAAA,CAAChB,QAAQ,EACPuC,MAAM,MAENC,QAAQ,CAAEd,QAAQ,CAACe,QAAQ,GAAKH,IAAI,CAACP,IAAK,CAC1CW,OAAO,CAAEA,CAAA,GAAM,CACbjB,QAAQ,CAACa,IAAI,CAACP,IAAI,CAAC,CACnB,GAAIT,QAAQ,CAAEH,aAAa,CAAC,KAAK,CAAC,CACpC,CAAE,CAAAc,QAAA,eAEFnB,IAAA,CAACb,YAAY,EAAAgC,QAAA,CAAEK,IAAI,CAACR,IAAI,CAAe,CAAC,cACxChB,IAAA,CAACZ,YAAY,EAACyC,OAAO,CAAEL,IAAI,CAACT,IAAK,CAAE,CAAC,GAR/BS,IAAI,CAACT,IASF,CACX,CAAC,CACE,CAAC,EACJ,CACN,CAED,mBACEb,KAAA,CAACnB,GAAG,EAAC+C,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAO,CAAE,CAAAZ,QAAA,eAC3BnB,IAAA,CAACrB,MAAM,EACLqD,QAAQ,CAAC,OAAO,CAChBF,EAAE,CAAE,CACFG,KAAK,CAAE,CAAEC,EAAE,qBAAuB,CAAC,CACnCC,EAAE,CAAE,CAAED,EAAE,CAAE,OAAQ,CACpB,CAAE,CAAAf,QAAA,cAEFjB,KAAA,CAACtB,OAAO,EAAAuC,QAAA,eACNnB,IAAA,CAAClB,UAAU,EACTsD,KAAK,CAAC,SAAS,CACf,aAAW,aAAa,CACxBC,IAAI,CAAC,OAAO,CACZT,OAAO,CAAEf,kBAAmB,CAC5BiB,EAAE,CAAE,CAAEQ,EAAE,CAAE,CAAC,CAAEP,OAAO,CAAE,CAAEG,EAAE,CAAE,MAAO,CAAE,CAAE,CAAAf,QAAA,cAEvCnB,IAAA,CAACR,QAAQ,GAAE,CAAC,CACF,CAAC,cACbQ,IAAA,CAACnB,UAAU,EAACuC,OAAO,CAAC,IAAI,CAACC,MAAM,MAACC,SAAS,CAAC,KAAK,CAAAH,QAAA,CAAC,mCAEhD,CAAY,CAAC,EACN,CAAC,CACJ,CAAC,cAETjB,KAAA,CAACnB,GAAG,EACFuC,SAAS,CAAC,KAAK,CACfQ,EAAE,CAAE,CAAEG,KAAK,CAAE,CAAEC,EAAE,CAAE,GAAI,CAAC,CAAEK,UAAU,CAAE,CAAEL,EAAE,CAAE,CAAE,CAAE,CAAE,CAAAf,QAAA,eAElDnB,IAAA,CAAChB,MAAM,EACLoC,OAAO,CAAC,WAAW,CACnBoB,IAAI,CAAEpC,UAAW,CACjBqC,OAAO,CAAE5B,kBAAmB,CAC5B6B,UAAU,CAAE,CAAEC,WAAW,CAAE,IAAK,CAAE,CAClCb,EAAE,CAAE,CACFC,OAAO,CAAE,CAAEa,EAAE,CAAE,OAAO,CAAEV,EAAE,CAAE,MAAO,CAAC,CACpC,oBAAoB,CAAE,CAAEW,SAAS,CAAE,YAAY,CAAEZ,KAAK,CAAE,GAAI,CAC9D,CAAE,CAAAd,QAAA,CAEDD,MAAM,CACD,CAAC,cACTlB,IAAA,CAAChB,MAAM,EACLoC,OAAO,CAAC,WAAW,CACnBU,EAAE,CAAE,CACFC,OAAO,CAAE,CAAEa,EAAE,CAAE,MAAM,CAAEV,EAAE,CAAE,OAAQ,CAAC,CACpC,oBAAoB,CAAE,CAAEW,SAAS,CAAE,YAAY,CAAEZ,KAAK,CAAE,GAAI,CAC9D,CAAE,CACFO,IAAI,MAAArB,QAAA,CAEHD,MAAM,CACD,CAAC,EACN,CAAC,cAENlB,IAAA,CAACjB,GAAG,EACFuC,SAAS,CAAC,MAAM,CAChBQ,EAAE,CAAE,CACFgB,QAAQ,CAAE,CAAC,CACXC,CAAC,CAAE,CAAC,CACJd,KAAK,CAAE,CAAEC,EAAE,qBAAuB,CAAC,CACnCc,EAAE,CAAE,MACN,CAAE,CAAA7B,QAAA,cAEFnB,IAAA,CAACxB,MAAM,GAAE,CAAC,CACP,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA2B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}