{"ast": null, "code": "export { default } from './TimelineOppositeContent';\nexport { default as timelineOppositeContentClasses } from './timelineOppositeContentClasses';\nexport * from './timelineOppositeContentClasses';", "map": {"version": 3, "names": ["default", "timelineOppositeContentClasses"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/TimelineOppositeContent/index.js"], "sourcesContent": ["export { default } from './TimelineOppositeContent';\nexport { default as timelineOppositeContentClasses } from './timelineOppositeContentClasses';\nexport * from './timelineOppositeContentClasses';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASA,OAAO,IAAIC,8BAA8B,QAAQ,kCAAkC;AAC5F,cAAc,kCAAkC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}