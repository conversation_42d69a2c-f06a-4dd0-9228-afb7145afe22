[{"C:\\NewSiteKevin\\frontend\\src\\index.js": "1", "C:\\NewSiteKevin\\frontend\\src\\App.js": "2", "C:\\NewSiteKevin\\frontend\\src\\components\\DispatchDashboard.jsx": "3", "C:\\NewSiteKevin\\frontend\\src\\components\\driver\\DriverDashboard.jsx": "4", "C:\\NewSiteKevin\\frontend\\src\\components\\GeofenceManager.jsx": "5", "C:\\NewSiteKevin\\frontend\\src\\components\\OrderForm.jsx": "6", "C:\\NewSiteKevin\\frontend\\src\\components\\MaterialCatalog.jsx": "7", "C:\\NewSiteKevin\\frontend\\src\\components\\driver\\DriverLogin.jsx": "8", "C:\\NewSiteKevin\\frontend\\src\\components\\driver\\DeliveryDetails.jsx": "9", "C:\\NewSiteKevin\\frontend\\src\\components\\driver\\DriverLayout.jsx": "10", "C:\\NewSiteKevin\\frontend\\src\\components\\layout\\AdminLayout.jsx": "11", "C:\\NewSiteKevin\\frontend\\src\\components\\PurchaseOrderForm.jsx": "12"}, {"size": 232, "mtime": 1748974777766, "results": "13", "hashOfConfig": "14"}, {"size": 2173, "mtime": 1748976147478, "results": "15", "hashOfConfig": "14"}, {"size": 4870, "mtime": 1748975235135, "results": "16", "hashOfConfig": "14"}, {"size": 11866, "mtime": 1748974818317, "results": "17", "hashOfConfig": "14"}, {"size": 10564, "mtime": 1748974912029, "results": "18", "hashOfConfig": "14"}, {"size": 4582, "mtime": 1748974365335, "results": "19", "hashOfConfig": "14"}, {"size": 2968, "mtime": 1748974368980, "results": "20", "hashOfConfig": "14"}, {"size": 3610, "mtime": 1748974846799, "results": "21", "hashOfConfig": "14"}, {"size": 4604, "mtime": 1748974866180, "results": "22", "hashOfConfig": "14"}, {"size": 3346, "mtime": 1748974833035, "results": "23", "hashOfConfig": "14"}, {"size": 3441, "mtime": 1748976170636, "results": "24", "hashOfConfig": "14"}, {"size": 14954, "mtime": 1748976043264, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1c8cu4y", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\NewSiteKevin\\frontend\\src\\index.js", [], [], "C:\\NewSiteKevin\\frontend\\src\\App.js", [], [], "C:\\NewSiteKevin\\frontend\\src\\components\\DispatchDashboard.jsx", ["62", "63", "64", "65", "66", "67", "68", "69", "70"], [], "C:\\NewSiteKevin\\frontend\\src\\components\\driver\\DriverDashboard.jsx", ["71", "72", "73"], [], "C:\\NewSiteKevin\\frontend\\src\\components\\GeofenceManager.jsx", ["74", "75", "76", "77", "78", "79", "80", "81", "82"], [], "C:\\NewSiteKevin\\frontend\\src\\components\\OrderForm.jsx", [], [], "C:\\NewSiteKevin\\frontend\\src\\components\\MaterialCatalog.jsx", ["83", "84"], [], "C:\\NewSiteKevin\\frontend\\src\\components\\driver\\DriverLogin.jsx", [], [], "C:\\NewSiteKevin\\frontend\\src\\components\\driver\\DeliveryDetails.jsx", ["85", "86", "87"], [], "C:\\NewSiteKevin\\frontend\\src\\components\\driver\\DriverLayout.jsx", [], [], "C:\\NewSiteKevin\\frontend\\src\\components\\layout\\AdminLayout.jsx", [], [], "C:\\NewSiteKevin\\frontend\\src\\components\\PurchaseOrderForm.jsx", ["88"], [], {"ruleId": "89", "severity": 1, "message": "90", "line": 6, "column": 10, "nodeType": "91", "messageId": "92", "endLine": 6, "endColumn": 18}, {"ruleId": "89", "severity": 1, "message": "93", "line": 6, "column": 20, "nodeType": "91", "messageId": "92", "endLine": 6, "endColumn": 32}, {"ruleId": "89", "severity": 1, "message": "94", "line": 6, "column": 34, "nodeType": "91", "messageId": "92", "endLine": 6, "endColumn": 51}, {"ruleId": "89", "severity": 1, "message": "95", "line": 6, "column": 53, "nodeType": "91", "messageId": "92", "endLine": 6, "endColumn": 70}, {"ruleId": "89", "severity": 1, "message": "96", "line": 7, "column": 3, "nodeType": "91", "messageId": "92", "endLine": 7, "endColumn": 18}, {"ruleId": "89", "severity": 1, "message": "97", "line": 7, "column": 20, "nodeType": "91", "messageId": "92", "endLine": 7, "endColumn": 31}, {"ruleId": "89", "severity": 1, "message": "98", "line": 14, "column": 22, "nodeType": "91", "messageId": "92", "endLine": 14, "endColumn": 35}, {"ruleId": "89", "severity": 1, "message": "99", "line": 30, "column": 9, "nodeType": "91", "messageId": "92", "endLine": 30, "endColumn": 23}, {"ruleId": "89", "severity": 1, "message": "100", "line": 45, "column": 9, "nodeType": "91", "messageId": "92", "endLine": 45, "endColumn": 23}, {"ruleId": "89", "severity": 1, "message": "101", "line": 1, "column": 38, "nodeType": "91", "messageId": "92", "endLine": 1, "endColumn": 49}, {"ruleId": "89", "severity": 1, "message": "102", "line": 9, "column": 3, "nodeType": "91", "messageId": "92", "endLine": 9, "endColumn": 10}, {"ruleId": "103", "severity": 1, "message": "104", "line": 26, "column": 6, "nodeType": "105", "endLine": 26, "endColumn": 8, "suggestions": "106"}, {"ruleId": "89", "severity": 1, "message": "107", "line": 7, "column": 3, "nodeType": "91", "messageId": "92", "endLine": 7, "endColumn": 8}, {"ruleId": "89", "severity": 1, "message": "108", "line": 7, "column": 10, "nodeType": "91", "messageId": "92", "endLine": 7, "endColumn": 19}, {"ruleId": "89", "severity": 1, "message": "109", "line": 7, "column": 21, "nodeType": "91", "messageId": "92", "endLine": 7, "endColumn": 30}, {"ruleId": "89", "severity": 1, "message": "110", "line": 7, "column": 32, "nodeType": "91", "messageId": "92", "endLine": 7, "endColumn": 46}, {"ruleId": "89", "severity": 1, "message": "111", "line": 8, "column": 3, "nodeType": "91", "messageId": "92", "endLine": 8, "endColumn": 12}, {"ruleId": "89", "severity": 1, "message": "112", "line": 8, "column": 14, "nodeType": "91", "messageId": "92", "endLine": 8, "endColumn": 22}, {"ruleId": "89", "severity": 1, "message": "113", "line": 8, "column": 24, "nodeType": "91", "messageId": "92", "endLine": 8, "endColumn": 29}, {"ruleId": "89", "severity": 1, "message": "114", "line": 11, "column": 34, "nodeType": "91", "messageId": "92", "endLine": 11, "endColumn": 44}, {"ruleId": "89", "severity": 1, "message": "115", "line": 12, "column": 10, "nodeType": "91", "messageId": "92", "endLine": 12, "endColumn": 17}, {"ruleId": "89", "severity": 1, "message": "116", "line": 4, "column": 39, "nodeType": "91", "messageId": "92", "endLine": 4, "endColumn": 48}, {"ruleId": "89", "severity": 1, "message": "117", "line": 12, "column": 23, "nodeType": "91", "messageId": "92", "endLine": 12, "endColumn": 37}, {"ruleId": "89", "severity": 1, "message": "118", "line": 9, "column": 25, "nodeType": "91", "messageId": "92", "endLine": 9, "endColumn": 38}, {"ruleId": "89", "severity": 1, "message": "119", "line": 10, "column": 3, "nodeType": "91", "messageId": "92", "endLine": 10, "endColumn": 14}, {"ruleId": "103", "severity": 1, "message": "120", "line": 22, "column": 6, "nodeType": "105", "endLine": 22, "endColumn": 10, "suggestions": "121"}, {"ruleId": "89", "severity": 1, "message": "122", "line": 5, "column": 54, "nodeType": "91", "messageId": "92", "endLine": 5, "endColumn": 58}, "no-unused-vars", "'Timeline' is defined but never used.", "Identifier", "unusedVar", "'TimelineItem' is defined but never used.", "'TimelineSeparator' is defined but never used.", "'TimelineConnector' is defined but never used.", "'TimelineContent' is defined but never used.", "'TimelineDot' is defined but never used.", "'setFilterDate' is assigned a value but never used.", "'assignDelivery' is assigned a value but never used.", "'getStatusColor' is assigned a value but never used.", "'useCallback' is defined but never used.", "'Warning' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'initializeLocation'. Either include it or remove the dependency array.", "ArrayExpression", ["123"], "'Table' is defined but never used.", "'TableBody' is defined but never used.", "'TableCell' is defined but never used.", "'TableContainer' is defined but never used.", "'TableHead' is defined but never used.", "'TableRow' is defined but never used.", "'Paper' is defined but never used.", "'Visibility' is defined but never used.", "'MapIcon' is defined but never used.", "'TextField' is defined but never used.", "'setNewMaterial' is assigned a value but never used.", "'LocalShipping' is defined but never used.", "'CheckCircle' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchDeliveryDetails'. Either include it or remove the dependency array.", ["124"], "'Chip' is defined but never used.", {"desc": "125", "fix": "126"}, {"desc": "127", "fix": "128"}, "Update the dependencies array to be: [initializeLocation]", {"range": "129", "text": "130"}, "Update the dependencies array to be: [fetchDeliveryDetails, id]", {"range": "131", "text": "132"}, [972, 974], "[initializeLocation]", [642, 646], "[fetchDeliveryDetails, id]"]