[{"C:\\NewSiteKevin\\frontend\\src\\index.js": "1", "C:\\NewSiteKevin\\frontend\\src\\App.js": "2", "C:\\NewSiteKevin\\frontend\\src\\components\\DispatchDashboard.jsx": "3", "C:\\NewSiteKevin\\frontend\\src\\components\\driver\\DriverDashboard.jsx": "4", "C:\\NewSiteKevin\\frontend\\src\\components\\GeofenceManager.jsx": "5", "C:\\NewSiteKevin\\frontend\\src\\components\\OrderForm.jsx": "6", "C:\\NewSiteKevin\\frontend\\src\\components\\MaterialCatalog.jsx": "7", "C:\\NewSiteKevin\\frontend\\src\\components\\driver\\DriverLogin.jsx": "8", "C:\\NewSiteKevin\\frontend\\src\\components\\driver\\DeliveryDetails.jsx": "9", "C:\\NewSiteKevin\\frontend\\src\\components\\driver\\DriverLayout.jsx": "10", "C:\\NewSiteKevin\\frontend\\src\\components\\layout\\AdminLayout.jsx": "11", "C:\\NewSiteKevin\\frontend\\src\\components\\PurchaseOrderForm.jsx": "12", "C:\\NewSiteKevin\\frontend\\src\\theme\\modernTheme.js": "13", "C:\\NewSiteKevin\\frontend\\src\\contexts\\LanguageContext.js": "14", "C:\\NewSiteKevin\\frontend\\src\\components\\auth\\ModernLogin.jsx": "15"}, {"size": 232, "mtime": 1748974777766, "results": "16", "hashOfConfig": "17"}, {"size": 2459, "mtime": 1748976942245, "results": "18", "hashOfConfig": "17"}, {"size": 4870, "mtime": 1748975235135, "results": "19", "hashOfConfig": "17"}, {"size": 11866, "mtime": 1748974818317, "results": "20", "hashOfConfig": "17"}, {"size": 10564, "mtime": 1748974912029, "results": "21", "hashOfConfig": "17"}, {"size": 4582, "mtime": 1748974365335, "results": "22", "hashOfConfig": "17"}, {"size": 2968, "mtime": 1748974368980, "results": "23", "hashOfConfig": "17"}, {"size": 3610, "mtime": 1748974846799, "results": "24", "hashOfConfig": "17"}, {"size": 4604, "mtime": 1748974866180, "results": "25", "hashOfConfig": "17"}, {"size": 3346, "mtime": 1748974833035, "results": "26", "hashOfConfig": "17"}, {"size": 4033, "mtime": 1748977012420, "results": "27", "hashOfConfig": "17"}, {"size": 16256, "mtime": 1748977095093, "results": "28", "hashOfConfig": "17"}, {"size": 6577, "mtime": 1748976775691, "results": "29", "hashOfConfig": "17"}, {"size": 5063, "mtime": 1748976742034, "results": "30", "hashOfConfig": "17"}, {"size": 9048, "mtime": 1748976813168, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1c8cu4y", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\NewSiteKevin\\frontend\\src\\index.js", [], [], "C:\\NewSiteKevin\\frontend\\src\\App.js", [], [], "C:\\NewSiteKevin\\frontend\\src\\components\\DispatchDashboard.jsx", ["77", "78", "79", "80", "81", "82", "83", "84", "85"], [], "C:\\NewSiteKevin\\frontend\\src\\components\\driver\\DriverDashboard.jsx", ["86", "87", "88"], [], "C:\\NewSiteKevin\\frontend\\src\\components\\GeofenceManager.jsx", ["89", "90", "91", "92", "93", "94", "95", "96", "97"], [], "C:\\NewSiteKevin\\frontend\\src\\components\\OrderForm.jsx", [], [], "C:\\NewSiteKevin\\frontend\\src\\components\\MaterialCatalog.jsx", ["98", "99"], [], "C:\\NewSiteKevin\\frontend\\src\\components\\driver\\DriverLogin.jsx", [], [], "C:\\NewSiteKevin\\frontend\\src\\components\\driver\\DeliveryDetails.jsx", ["100", "101", "102"], [], "C:\\NewSiteKevin\\frontend\\src\\components\\driver\\DriverLayout.jsx", [], [], "C:\\NewSiteKevin\\frontend\\src\\components\\layout\\AdminLayout.jsx", [], [], "C:\\NewSiteKevin\\frontend\\src\\components\\PurchaseOrderForm.jsx", ["103", "104"], [], "C:\\NewSiteKevin\\frontend\\src\\theme\\modernTheme.js", [], [], "C:\\NewSiteKevin\\frontend\\src\\contexts\\LanguageContext.js", [], [], "C:\\NewSiteKevin\\frontend\\src\\components\\auth\\ModernLogin.jsx", [], [], {"ruleId": "105", "severity": 1, "message": "106", "line": 6, "column": 10, "nodeType": "107", "messageId": "108", "endLine": 6, "endColumn": 18}, {"ruleId": "105", "severity": 1, "message": "109", "line": 6, "column": 20, "nodeType": "107", "messageId": "108", "endLine": 6, "endColumn": 32}, {"ruleId": "105", "severity": 1, "message": "110", "line": 6, "column": 34, "nodeType": "107", "messageId": "108", "endLine": 6, "endColumn": 51}, {"ruleId": "105", "severity": 1, "message": "111", "line": 6, "column": 53, "nodeType": "107", "messageId": "108", "endLine": 6, "endColumn": 70}, {"ruleId": "105", "severity": 1, "message": "112", "line": 7, "column": 3, "nodeType": "107", "messageId": "108", "endLine": 7, "endColumn": 18}, {"ruleId": "105", "severity": 1, "message": "113", "line": 7, "column": 20, "nodeType": "107", "messageId": "108", "endLine": 7, "endColumn": 31}, {"ruleId": "105", "severity": 1, "message": "114", "line": 14, "column": 22, "nodeType": "107", "messageId": "108", "endLine": 14, "endColumn": 35}, {"ruleId": "105", "severity": 1, "message": "115", "line": 30, "column": 9, "nodeType": "107", "messageId": "108", "endLine": 30, "endColumn": 23}, {"ruleId": "105", "severity": 1, "message": "116", "line": 45, "column": 9, "nodeType": "107", "messageId": "108", "endLine": 45, "endColumn": 23}, {"ruleId": "105", "severity": 1, "message": "117", "line": 1, "column": 38, "nodeType": "107", "messageId": "108", "endLine": 1, "endColumn": 49}, {"ruleId": "105", "severity": 1, "message": "118", "line": 9, "column": 3, "nodeType": "107", "messageId": "108", "endLine": 9, "endColumn": 10}, {"ruleId": "119", "severity": 1, "message": "120", "line": 26, "column": 6, "nodeType": "121", "endLine": 26, "endColumn": 8, "suggestions": "122"}, {"ruleId": "105", "severity": 1, "message": "123", "line": 7, "column": 3, "nodeType": "107", "messageId": "108", "endLine": 7, "endColumn": 8}, {"ruleId": "105", "severity": 1, "message": "124", "line": 7, "column": 10, "nodeType": "107", "messageId": "108", "endLine": 7, "endColumn": 19}, {"ruleId": "105", "severity": 1, "message": "125", "line": 7, "column": 21, "nodeType": "107", "messageId": "108", "endLine": 7, "endColumn": 30}, {"ruleId": "105", "severity": 1, "message": "126", "line": 7, "column": 32, "nodeType": "107", "messageId": "108", "endLine": 7, "endColumn": 46}, {"ruleId": "105", "severity": 1, "message": "127", "line": 8, "column": 3, "nodeType": "107", "messageId": "108", "endLine": 8, "endColumn": 12}, {"ruleId": "105", "severity": 1, "message": "128", "line": 8, "column": 14, "nodeType": "107", "messageId": "108", "endLine": 8, "endColumn": 22}, {"ruleId": "105", "severity": 1, "message": "129", "line": 8, "column": 24, "nodeType": "107", "messageId": "108", "endLine": 8, "endColumn": 29}, {"ruleId": "105", "severity": 1, "message": "130", "line": 11, "column": 34, "nodeType": "107", "messageId": "108", "endLine": 11, "endColumn": 44}, {"ruleId": "105", "severity": 1, "message": "131", "line": 12, "column": 10, "nodeType": "107", "messageId": "108", "endLine": 12, "endColumn": 17}, {"ruleId": "105", "severity": 1, "message": "132", "line": 4, "column": 39, "nodeType": "107", "messageId": "108", "endLine": 4, "endColumn": 48}, {"ruleId": "105", "severity": 1, "message": "133", "line": 12, "column": 23, "nodeType": "107", "messageId": "108", "endLine": 12, "endColumn": 37}, {"ruleId": "105", "severity": 1, "message": "134", "line": 9, "column": 25, "nodeType": "107", "messageId": "108", "endLine": 9, "endColumn": 38}, {"ruleId": "105", "severity": 1, "message": "135", "line": 10, "column": 3, "nodeType": "107", "messageId": "108", "endLine": 10, "endColumn": 14}, {"ruleId": "119", "severity": 1, "message": "136", "line": 22, "column": 6, "nodeType": "121", "endLine": 22, "endColumn": 10, "suggestions": "137"}, {"ruleId": "105", "severity": 1, "message": "138", "line": 5, "column": 54, "nodeType": "107", "messageId": "108", "endLine": 5, "endColumn": 58}, {"ruleId": "105", "severity": 1, "message": "134", "line": 10, "column": 15, "nodeType": "107", "messageId": "108", "endLine": 10, "endColumn": 28}, "no-unused-vars", "'Timeline' is defined but never used.", "Identifier", "unusedVar", "'TimelineItem' is defined but never used.", "'TimelineSeparator' is defined but never used.", "'TimelineConnector' is defined but never used.", "'TimelineContent' is defined but never used.", "'TimelineDot' is defined but never used.", "'setFilterDate' is assigned a value but never used.", "'assignDelivery' is assigned a value but never used.", "'getStatusColor' is assigned a value but never used.", "'useCallback' is defined but never used.", "'Warning' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'initializeLocation'. Either include it or remove the dependency array.", "ArrayExpression", ["139"], "'Table' is defined but never used.", "'TableBody' is defined but never used.", "'TableCell' is defined but never used.", "'TableContainer' is defined but never used.", "'TableHead' is defined but never used.", "'TableRow' is defined but never used.", "'Paper' is defined but never used.", "'Visibility' is defined but never used.", "'MapIcon' is defined but never used.", "'TextField' is defined but never used.", "'setNewMaterial' is assigned a value but never used.", "'LocalShipping' is defined but never used.", "'CheckCircle' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchDeliveryDetails'. Either include it or remove the dependency array.", ["140"], "'Chip' is defined but never used.", {"desc": "141", "fix": "142"}, {"desc": "143", "fix": "144"}, "Update the dependencies array to be: [initializeLocation]", {"range": "145", "text": "146"}, "Update the dependencies array to be: [fetchDeliveryDetails, id]", {"range": "147", "text": "148"}, [972, 974], "[initializeLocation]", [642, 646], "[fetchDeliveryDetails, id]"]