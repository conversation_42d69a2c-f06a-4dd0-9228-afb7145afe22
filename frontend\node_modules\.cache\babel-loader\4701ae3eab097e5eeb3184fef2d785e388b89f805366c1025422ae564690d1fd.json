{"ast": null, "code": "export { FocusTrap } from './FocusTrap';\nexport * from './FocusTrap.types';", "map": {"version": 3, "names": ["FocusTrap"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/base/FocusTrap/index.js"], "sourcesContent": ["export { FocusTrap } from './FocusTrap';\nexport * from './FocusTrap.types';"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AACvC,cAAc,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}