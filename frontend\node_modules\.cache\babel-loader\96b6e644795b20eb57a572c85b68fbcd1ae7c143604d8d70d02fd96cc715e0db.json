{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\n\n/**\n * @type {React.Context<{ idPrefix: string; value: string } | null>}\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Context = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== 'production') {\n  Context.displayName = 'TabContext';\n}\nfunction useUniquePrefix() {\n  const [id, setId] = React.useState(null);\n  React.useEffect(() => {\n    setId(\"mui-p-\".concat(Math.round(Math.random() * 1e5)));\n  }, []);\n  return id;\n}\nexport default function TabContext(props) {\n  const {\n    children,\n    value\n  } = props;\n  const idPrefix = useUniquePrefix();\n  const context = React.useMemo(() => {\n    return {\n      idPrefix,\n      value\n    };\n  }, [idPrefix, value]);\n  return /*#__PURE__*/_jsx(Context.Provider, {\n    value: context,\n    children: children\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? TabContext.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * The value of the currently selected `Tab`.\n   */\n  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired\n} : void 0;\n\n/**\n * @returns {unknown}\n */\nexport function useTabContext() {\n  return React.useContext(Context);\n}\nexport function getPanelId(context, value) {\n  const {\n    idPrefix\n  } = context;\n  if (idPrefix === null) {\n    return null;\n  }\n  return \"\".concat(context.idPrefix, \"-P-\").concat(value);\n}\nexport function getTabId(context, value) {\n  const {\n    idPrefix\n  } = context;\n  if (idPrefix === null) {\n    return null;\n  }\n  return \"\".concat(context.idPrefix, \"-T-\").concat(value);\n}", "map": {"version": 3, "names": ["React", "PropTypes", "jsx", "_jsx", "Context", "createContext", "process", "env", "NODE_ENV", "displayName", "useUniquePrefix", "id", "setId", "useState", "useEffect", "concat", "Math", "round", "random", "TabContext", "props", "children", "value", "idPrefix", "context", "useMemo", "Provider", "propTypes", "node", "oneOfType", "number", "string", "isRequired", "useTabContext", "useContext", "getPanelId", "getTabId"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/TabContext/TabContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\n\n/**\n * @type {React.Context<{ idPrefix: string; value: string } | null>}\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Context = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== 'production') {\n  Context.displayName = 'TabContext';\n}\nfunction useUniquePrefix() {\n  const [id, setId] = React.useState(null);\n  React.useEffect(() => {\n    setId(`mui-p-${Math.round(Math.random() * 1e5)}`);\n  }, []);\n  return id;\n}\nexport default function TabContext(props) {\n  const {\n    children,\n    value\n  } = props;\n  const idPrefix = useUniquePrefix();\n  const context = React.useMemo(() => {\n    return {\n      idPrefix,\n      value\n    };\n  }, [idPrefix, value]);\n  return /*#__PURE__*/_jsx(Context.Provider, {\n    value: context,\n    children: children\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? TabContext.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * The value of the currently selected `Tab`.\n   */\n  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired\n} : void 0;\n\n/**\n * @returns {unknown}\n */\nexport function useTabContext() {\n  return React.useContext(Context);\n}\nexport function getPanelId(context, value) {\n  const {\n    idPrefix\n  } = context;\n  if (idPrefix === null) {\n    return null;\n  }\n  return `${context.idPrefix}-P-${value}`;\n}\nexport function getTabId(context, value) {\n  const {\n    idPrefix\n  } = context;\n  if (idPrefix === null) {\n    return null;\n  }\n  return `${context.idPrefix}-T-${value}`;\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;;AAElC;AACA;AACA;AACA,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,OAAO,GAAG,aAAaJ,KAAK,CAACK,aAAa,CAAC,IAAI,CAAC;AACtD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,YAAY;AACpC;AACA,SAASC,eAAeA,CAAA,EAAG;EACzB,MAAM,CAACC,EAAE,EAAEC,KAAK,CAAC,GAAGZ,KAAK,CAACa,QAAQ,CAAC,IAAI,CAAC;EACxCb,KAAK,CAACc,SAAS,CAAC,MAAM;IACpBF,KAAK,UAAAG,MAAA,CAAUC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,CAAE,CAAC;EACnD,CAAC,EAAE,EAAE,CAAC;EACN,OAAOP,EAAE;AACX;AACA,eAAe,SAASQ,UAAUA,CAACC,KAAK,EAAE;EACxC,MAAM;IACJC,QAAQ;IACRC;EACF,CAAC,GAAGF,KAAK;EACT,MAAMG,QAAQ,GAAGb,eAAe,CAAC,CAAC;EAClC,MAAMc,OAAO,GAAGxB,KAAK,CAACyB,OAAO,CAAC,MAAM;IAClC,OAAO;MACLF,QAAQ;MACRD;IACF,CAAC;EACH,CAAC,EAAE,CAACC,QAAQ,EAAED,KAAK,CAAC,CAAC;EACrB,OAAO,aAAanB,IAAI,CAACC,OAAO,CAACsB,QAAQ,EAAE;IACzCJ,KAAK,EAAEE,OAAO;IACdH,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ;AACAf,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGW,UAAU,CAACQ,SAAS,CAAC,yBAAyB;EACpF;EACA;EACA;EACA;EACA;AACF;AACA;EACEN,QAAQ,EAAEpB,SAAS,CAAC2B,IAAI;EACxB;AACF;AACA;EACEN,KAAK,EAAErB,SAAS,CAAC4B,SAAS,CAAC,CAAC5B,SAAS,CAAC6B,MAAM,EAAE7B,SAAS,CAAC8B,MAAM,CAAC,CAAC,CAACC;AACnE,CAAC,GAAG,KAAK,CAAC;;AAEV;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAAA,EAAG;EAC9B,OAAOjC,KAAK,CAACkC,UAAU,CAAC9B,OAAO,CAAC;AAClC;AACA,OAAO,SAAS+B,UAAUA,CAACX,OAAO,EAAEF,KAAK,EAAE;EACzC,MAAM;IACJC;EACF,CAAC,GAAGC,OAAO;EACX,IAAID,QAAQ,KAAK,IAAI,EAAE;IACrB,OAAO,IAAI;EACb;EACA,UAAAR,MAAA,CAAUS,OAAO,CAACD,QAAQ,SAAAR,MAAA,CAAMO,KAAK;AACvC;AACA,OAAO,SAASc,QAAQA,CAACZ,OAAO,EAAEF,KAAK,EAAE;EACvC,MAAM;IACJC;EACF,CAAC,GAAGC,OAAO;EACX,IAAID,QAAQ,KAAK,IAAI,EAAE;IACrB,OAAO,IAAI;EACb;EACA,UAAAR,MAAA,CAAUS,OAAO,CAACD,QAAQ,SAAAR,MAAA,CAAMO,KAAK;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}