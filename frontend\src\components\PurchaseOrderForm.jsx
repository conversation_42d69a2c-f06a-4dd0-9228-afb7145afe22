import React, { useState, useEffect } from 'react';
import {
  Container, Paper, Typography, Grid, TextField, Button,
  FormControl, InputLabel, Select, MenuItem, Card, CardContent,
  CardActions, IconButton, Box, Alert, Autocomplete, Chip,
  Fade, Slide
} from '@mui/material';
import {
  Add, Delete, Save, LocationOn, Calculate, Receipt,
  TrendingUp, LocalShipping
} from '@mui/icons-material';
import { useLanguage } from '../contexts/LanguageContext';
import axios from 'axios';

const PurchaseOrderForm = () => {
  const [clients, setClients] = useState([]);
  const [materials, setMaterials] = useState([]);
  const [materialCategories, setMaterialCategories] = useState([]);
  const [unitMeasures, setUnitMeasures] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const { t } = useLanguage();

  const [purchaseOrder, setPurchaseOrder] = useState({
    client_id: '',
    pickup_location: 'Main Quarry - 2500 Industrial Blvd, West Palm Beach, FL',
    pickup_latitude: 26.7153,
    pickup_longitude: -80.0534,
    notes: '',
    line_items: [{
      material_id: '',
      unit_measure_id: '',
      quantity: '',
      unit_price: '',
      hauling_rate: '',
      dropoff_location: '',
      dropoff_latitude: '',
      dropoff_longitude: '',
      special_instructions: ''
    }]
  });

  // Boynton Beach area addresses for autocomplete
  const commonAddresses = [
    { label: '1605 Renaissance Commons Blvd, Boynton Beach, FL 33426', lat: 26.5284, lng: -80.0831 },
    { label: '2200 Linton Blvd, Delray Beach, FL 33445', lat: 26.4615, lng: -80.0728 },
    { label: '1500 NW 2nd Ave, Boca Raton, FL 33432', lat: 26.3683, lng: -80.1289 },
    { label: '1900 2nd Ave N, Lake Worth, FL 33461', lat: 26.6156, lng: -80.0670 },
    { label: '12800 Forest Hill Blvd, Wellington, FL 33414', lat: 26.6581, lng: -80.2411 },
    { label: '1000 Gateway Blvd, Boynton Beach, FL 33426', lat: 26.5320, lng: -80.0856 },
    { label: '2240 Woolbright Rd, Boynton Beach, FL 33426', lat: 26.5234, lng: -80.0789 },
    { label: '1801 N Congress Ave, Boynton Beach, FL 33426', lat: 26.5312, lng: -80.0728 },
    { label: '4801 Linton Blvd, Delray Beach, FL 33445', lat: 26.4615, lng: -80.0456 },
    { label: '3333 Forest Hill Blvd, West Palm Beach, FL 33406', lat: 26.6581, lng: -80.1534 }
  ];

  useEffect(() => {
    fetchInitialData();
  }, []);

  const fetchInitialData = async () => {
    try {
      const [clientsRes, materialsRes, categoriesRes, unitsRes] = await Promise.all([
        axios.get('/api/customers'),
        axios.get('/api/materials'),
        axios.get('/api/materials/categories'),
        axios.get('/api/materials/unit-measures')
      ]);

      setClients(clientsRes.data);
      setMaterials(materialsRes.data);
      setMaterialCategories(categoriesRes.data);
      setUnitMeasures(unitsRes.data);
    } catch (err) {
      setError('Failed to load form data');
      console.error('Error loading data:', err);
    }
  };

  const handleInputChange = (field, value) => {
    setPurchaseOrder(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleLineItemChange = (index, field, value) => {
    const newLineItems = [...purchaseOrder.line_items];
    newLineItems[index] = {
      ...newLineItems[index],
      [field]: value
    };

    // Auto-populate unit price when material is selected
    if (field === 'material_id' && value) {
      const material = materials.find(m => m.id === value);
      if (material) {
        newLineItems[index].unit_price = material.base_cost_per_unit;
        newLineItems[index].unit_measure_id = material.default_unit_measure_id;
      }
    }

    setPurchaseOrder(prev => ({
      ...prev,
      line_items: newLineItems
    }));
  };

  const addLineItem = () => {
    setPurchaseOrder(prev => ({
      ...prev,
      line_items: [...prev.line_items, {
        material_id: '',
        unit_measure_id: '',
        quantity: '',
        unit_price: '',
        hauling_rate: '',
        dropoff_location: '',
        dropoff_latitude: '',
        dropoff_longitude: '',
        special_instructions: ''
      }]
    }));
  };

  const removeLineItem = (index) => {
    if (purchaseOrder.line_items.length > 1) {
      setPurchaseOrder(prev => ({
        ...prev,
        line_items: prev.line_items.filter((_, i) => i !== index)
      }));
    }
  };

  const handleAddressSelect = (index, address) => {
    if (address) {
      handleLineItemChange(index, 'dropoff_location', address.label);
      handleLineItemChange(index, 'dropoff_latitude', address.lat);
      handleLineItemChange(index, 'dropoff_longitude', address.lng);
    }
  };

  const calculateTotal = () => {
    return purchaseOrder.line_items.reduce((total, item) => {
      const quantity = parseFloat(item.quantity) || 0;
      const unitPrice = parseFloat(item.unit_price) || 0;
      const haulingRate = parseFloat(item.hauling_rate) || 0;
      return total + (quantity * (unitPrice + haulingRate));
    }, 0);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await axios.post('/api/purchase-orders', purchaseOrder);
      setSuccess(`Purchase Order ${response.data.po_number} created successfully!`);
      
      // Reset form
      setPurchaseOrder({
        customer_id: '',
        pickup_location: 'Main Quarry - 2500 Industrial Blvd, West Palm Beach, FL',
        pickup_latitude: 26.7153,
        pickup_longitude: -80.0534,
        notes: '',
        line_items: [{
          material_id: '',
          unit_measure_id: '',
          quantity: '',
          unit_price: '',
          hauling_rate: '',
          dropoff_location: '',
          dropoff_latitude: '',
          dropoff_longitude: '',
          special_instructions: ''
        }]
      });
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to create purchase order');
    } finally {
      setLoading(false);
    }
  };

  const getMaterialsByCategory = (categoryId) => {
    return materials.filter(m => m.category_id === categoryId);
  };

  const getUnitMeasureName = (unitId) => {
    const unit = unitMeasures.find(u => u.id === unitId);
    return unit ? unit.abbreviation : '';
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Fade in timeout={800}>
        <Box>
          {/* Header */}
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
            <Receipt sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
            <Box>
              <Typography variant="h3" sx={{ fontWeight: 700, color: 'text.primary' }}>
                {t('create_purchase_order')}
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Create and manage purchase orders with automatic PO numbering
              </Typography>
            </Box>
          </Box>

          {error && (
            <Slide direction="down" in={!!error} mountOnEnter unmountOnExit>
              <Alert severity="error" sx={{ mb: 3, borderRadius: 2 }}>{error}</Alert>
            </Slide>
          )}
          {success && (
            <Slide direction="down" in={!!success} mountOnEnter unmountOnExit>
              <Alert severity="success" sx={{ mb: 3, borderRadius: 2 }}>{success}</Alert>
            </Slide>
          )}

          <form onSubmit={handleSubmit}>
            <Paper sx={{ p: 4, mb: 4, borderRadius: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <TrendingUp sx={{ color: 'primary.main', mr: 1 }} />
                <Typography variant="h5" sx={{ fontWeight: 600 }}>
                  {t('order_information')}
                </Typography>
              </Box>

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>{t('client')}</InputLabel>
                    <Select
                      value={purchaseOrder.client_id}
                      onChange={(e) => handleInputChange('client_id', e.target.value)}
                      required
                    >
                      {clients.map(client => (
                        <MenuItem key={client.id} value={client.id}>
                          {client.company_name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Pickup Location"
                value={purchaseOrder.pickup_location}
                onChange={(e) => handleInputChange('pickup_location', e.target.value)}
                InputProps={{
                  startAdornment: <LocationOn color="action" sx={{ mr: 1 }} />
                }}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes"
                multiline
                rows={2}
                value={purchaseOrder.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
              />
            </Grid>
          </Grid>
        </Paper>

        {/* Line Items */}
        <Typography variant="h6" gutterBottom>
          Line Items
        </Typography>

        {purchaseOrder.line_items.map((item, index) => (
          <Card key={index} sx={{ mb: 2 }}>
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>Material</InputLabel>
                    <Select
                      value={item.material_id}
                      onChange={(e) => handleLineItemChange(index, 'material_id', e.target.value)}
                      required
                    >
                      {materialCategories.map(category => [
                        <MenuItem key={`cat-${category.id}`} disabled sx={{ fontWeight: 'bold' }}>
                          {category.icon} {category.name}
                        </MenuItem>,
                        ...getMaterialsByCategory(category.id).map(material => (
                          <MenuItem key={material.id} value={material.id} sx={{ pl: 4 }}>
                            {material.name}
                          </MenuItem>
                        ))
                      ])}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={6} md={2}>
                  <TextField
                    fullWidth
                    label="Quantity"
                    type="number"
                    inputProps={{ step: '0.1', min: '0' }}
                    value={item.quantity}
                    onChange={(e) => handleLineItemChange(index, 'quantity', e.target.value)}
                    required
                  />
                </Grid>

                <Grid item xs={6} md={2}>
                  <FormControl fullWidth>
                    <InputLabel>Unit</InputLabel>
                    <Select
                      value={item.unit_measure_id}
                      onChange={(e) => handleLineItemChange(index, 'unit_measure_id', e.target.value)}
                      required
                    >
                      {unitMeasures.map(unit => (
                        <MenuItem key={unit.id} value={unit.id}>
                          {unit.abbreviation} ({unit.name})
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={6} md={2}>
                  <TextField
                    fullWidth
                    label="Unit Price"
                    type="number"
                    inputProps={{ step: '0.01', min: '0' }}
                    value={item.unit_price}
                    onChange={(e) => handleLineItemChange(index, 'unit_price', e.target.value)}
                    required
                  />
                </Grid>

                <Grid item xs={6} md={2}>
                  <TextField
                    fullWidth
                    label="Hauling Rate"
                    type="number"
                    inputProps={{ step: '0.01', min: '0' }}
                    value={item.hauling_rate}
                    onChange={(e) => handleLineItemChange(index, 'hauling_rate', e.target.value)}
                    required
                  />
                </Grid>

                <Grid item xs={12}>
                  <Autocomplete
                    options={commonAddresses}
                    getOptionLabel={(option) => option.label}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Dropoff Location"
                        required
                        InputProps={{
                          ...params.InputProps,
                          startAdornment: <LocationOn color="action" sx={{ mr: 1 }} />
                        }}
                      />
                    )}
                    value={commonAddresses.find(addr => addr.label === item.dropoff_location) || null}
                    onChange={(e, value) => handleAddressSelect(index, value)}
                    freeSolo
                    renderOption={(props, option) => (
                      <Box component="li" {...props}>
                        <LocationOn sx={{ mr: 2 }} />
                        {option.label}
                      </Box>
                    )}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Special Instructions"
                    multiline
                    rows={2}
                    value={item.special_instructions}
                    onChange={(e) => handleLineItemChange(index, 'special_instructions', e.target.value)}
                  />
                </Grid>

                {item.quantity && item.unit_price && item.hauling_rate && (
                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Calculate color="action" />
                      <Typography variant="body2">
                        Line Total: {item.quantity} {getUnitMeasureName(item.unit_measure_id)} × 
                        (${item.unit_price} + ${item.hauling_rate}) = 
                        <strong> ${(parseFloat(item.quantity) * (parseFloat(item.unit_price) + parseFloat(item.hauling_rate))).toFixed(2)}</strong>
                      </Typography>
                    </Box>
                  </Grid>
                )}
              </Grid>
            </CardContent>
            
            <CardActions>
              <IconButton
                onClick={() => removeLineItem(index)}
                disabled={purchaseOrder.line_items.length === 1}
                color="error"
              >
                <Delete />
              </IconButton>
            </CardActions>
          </Card>
        ))}

        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Button
            variant="outlined"
            startIcon={<Add />}
            onClick={addLineItem}
          >
            Add Line Item
          </Button>

          <Typography variant="h6">
            Total: ${calculateTotal().toFixed(2)}
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
          <Button
            type="submit"
            variant="contained"
            startIcon={<Save />}
            disabled={loading}
            size="large"
          >
            {loading ? 'Creating...' : 'Create Purchase Order'}
          </Button>
        </Box>
          </form>
        </Box>
      </Fade>
    </Container>
  );
};

export default PurchaseOrderForm;
