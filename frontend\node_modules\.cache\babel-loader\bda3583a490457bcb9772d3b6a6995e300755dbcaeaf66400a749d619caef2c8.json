{"ast": null, "code": "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getLoadingButtonUtilityClass(slot) {\n  return generateUtilityClass('MuiLoadingButton', slot);\n}\nconst loadingButtonClasses = generateUtilityClasses('MuiLoadingButton', ['root', 'loading', 'loadingIndicator', 'loadingIndicatorCenter', 'loadingIndicatorStart', 'loadingIndicatorEnd', 'endIconLoadingEnd', 'startIconLoadingStart']);\nexport default loadingButtonClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getLoadingButtonUtilityClass", "slot", "loadingButtonClasses"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/LoadingButton/loadingButtonClasses.js"], "sourcesContent": ["import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getLoadingButtonUtilityClass(slot) {\n  return generateUtilityClass('MuiLoadingButton', slot);\n}\nconst loadingButtonClasses = generateUtilityClasses('MuiLoadingButton', ['root', 'loading', 'loadingIndicator', 'loadingIndicatorCenter', 'loadingIndicatorStart', 'loadingIndicatorEnd', 'endIconLoadingEnd', 'startIconLoadingStart']);\nexport default loadingButtonClasses;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,sBAAsB,MAAM,mCAAmC;AACtE,OAAO,SAASC,4BAA4BA,CAACC,IAAI,EAAE;EACjD,OAAOH,oBAAoB,CAAC,kBAAkB,EAAEG,IAAI,CAAC;AACvD;AACA,MAAMC,oBAAoB,GAAGH,sBAAsB,CAAC,kBAAkB,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,kBAAkB,EAAE,wBAAwB,EAAE,uBAAuB,EAAE,qBAAqB,EAAE,mBAAmB,EAAE,uBAAuB,CAAC,CAAC;AACxO,eAAeG,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}