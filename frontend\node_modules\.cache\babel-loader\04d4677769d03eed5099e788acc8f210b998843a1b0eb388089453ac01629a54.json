{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"position\", \"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { isMuiElement } from '@mui/material/utils';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { timelineContentClasses } from '../TimelineContent';\nimport { timelineOppositeContentClasses } from '../TimelineOppositeContent';\nimport TimelineContext from '../Timeline/TimelineContext';\nimport { getTimelineItemUtilityClass } from './timelineItemClasses';\nimport convertTimelinePositionToClass from '../internal/convertTimelinePositionToClass';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    position,\n    classes,\n    hasOppositeContent\n  } = ownerState;\n  const slots = {\n    root: ['root', convertTimelinePositionToClass(position), !hasOppositeContent && 'missingOppositeContent']\n  };\n  return composeClasses(slots, getTimelineItemUtilityClass, classes);\n};\nconst TimelineItemRoot = styled('li', {\n  name: 'MuiTimelineItem',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[convertTimelinePositionToClass(ownerState.position)]];\n  }\n})(_ref => {\n  let {\n    ownerState\n  } = _ref;\n  return _extends({\n    listStyle: 'none',\n    display: 'flex',\n    position: 'relative',\n    minHeight: 70\n  }, ownerState.position === 'left' && {\n    flexDirection: 'row-reverse'\n  }, (ownerState.position === 'alternate' || ownerState.position === 'alternate-reverse') && {\n    [\"&:nth-of-type(\".concat(ownerState.position === 'alternate' ? 'even' : 'odd', \")\")]: {\n      flexDirection: 'row-reverse',\n      [\"& .\".concat(timelineContentClasses.root)]: {\n        textAlign: 'right'\n      },\n      [\"& .\".concat(timelineOppositeContentClasses.root)]: {\n        textAlign: 'left'\n      }\n    }\n  }, !ownerState.hasOppositeContent && {\n    '&::before': {\n      content: '\"\"',\n      flex: 1,\n      padding: '6px 16px'\n    }\n  });\n});\nconst TimelineItem = /*#__PURE__*/React.forwardRef(function TimelineItem(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimelineItem'\n  });\n  const {\n      position: positionProp,\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    position: positionContext\n  } = React.useContext(TimelineContext);\n  let hasOppositeContent = false;\n  React.Children.forEach(props.children, child => {\n    if (isMuiElement(child, ['TimelineOppositeContent'])) {\n      hasOppositeContent = true;\n    }\n  });\n  const ownerState = _extends({}, props, {\n    position: positionProp || positionContext || 'right',\n    hasOppositeContent\n  });\n  const classes = useUtilityClasses(ownerState);\n  const contextValue = React.useMemo(() => ({\n    position: ownerState.position\n  }), [ownerState.position]);\n  return /*#__PURE__*/_jsx(TimelineContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(TimelineItemRoot, _extends({\n      className: clsx(classes.root, className),\n      ownerState: ownerState,\n      ref: ref\n    }, other))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TimelineItem.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The position where the timeline's item should appear.\n   */\n  position: PropTypes.oneOf(['alternate-reverse', 'alternate', 'left', 'right']),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TimelineItem;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "isMuiElement", "styled", "useThemeProps", "unstable_composeClasses", "composeClasses", "timelineContentClasses", "timelineOppositeContentClasses", "TimelineContext", "getTimelineItemUtilityClass", "convertTimelinePositionToClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "position", "classes", "hasOppositeContent", "slots", "root", "TimelineItemRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "listStyle", "display", "minHeight", "flexDirection", "concat", "textAlign", "content", "flex", "padding", "TimelineItem", "forwardRef", "inProps", "ref", "positionProp", "className", "other", "positionContext", "useContext", "Children", "for<PERSON>ach", "children", "child", "contextValue", "useMemo", "Provider", "value", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "oneOf", "sx", "oneOfType", "arrayOf", "func", "bool"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/TimelineItem/TimelineItem.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"position\", \"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { isMuiElement } from '@mui/material/utils';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { timelineContentClasses } from '../TimelineContent';\nimport { timelineOppositeContentClasses } from '../TimelineOppositeContent';\nimport TimelineContext from '../Timeline/TimelineContext';\nimport { getTimelineItemUtilityClass } from './timelineItemClasses';\nimport convertTimelinePositionToClass from '../internal/convertTimelinePositionToClass';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    position,\n    classes,\n    hasOppositeContent\n  } = ownerState;\n  const slots = {\n    root: ['root', convertTimelinePositionToClass(position), !hasOppositeContent && 'missingOppositeContent']\n  };\n  return composeClasses(slots, getTimelineItemUtilityClass, classes);\n};\nconst TimelineItemRoot = styled('li', {\n  name: 'MuiTimelineItem',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[convertTimelinePositionToClass(ownerState.position)]];\n  }\n})(({\n  ownerState\n}) => _extends({\n  listStyle: 'none',\n  display: 'flex',\n  position: 'relative',\n  minHeight: 70\n}, ownerState.position === 'left' && {\n  flexDirection: 'row-reverse'\n}, (ownerState.position === 'alternate' || ownerState.position === 'alternate-reverse') && {\n  [`&:nth-of-type(${ownerState.position === 'alternate' ? 'even' : 'odd'})`]: {\n    flexDirection: 'row-reverse',\n    [`& .${timelineContentClasses.root}`]: {\n      textAlign: 'right'\n    },\n    [`& .${timelineOppositeContentClasses.root}`]: {\n      textAlign: 'left'\n    }\n  }\n}, !ownerState.hasOppositeContent && {\n  '&::before': {\n    content: '\"\"',\n    flex: 1,\n    padding: '6px 16px'\n  }\n}));\nconst TimelineItem = /*#__PURE__*/React.forwardRef(function TimelineItem(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimelineItem'\n  });\n  const {\n      position: positionProp,\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    position: positionContext\n  } = React.useContext(TimelineContext);\n  let hasOppositeContent = false;\n  React.Children.forEach(props.children, child => {\n    if (isMuiElement(child, ['TimelineOppositeContent'])) {\n      hasOppositeContent = true;\n    }\n  });\n  const ownerState = _extends({}, props, {\n    position: positionProp || positionContext || 'right',\n    hasOppositeContent\n  });\n  const classes = useUtilityClasses(ownerState);\n  const contextValue = React.useMemo(() => ({\n    position: ownerState.position\n  }), [ownerState.position]);\n  return /*#__PURE__*/_jsx(TimelineContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(TimelineItemRoot, _extends({\n      className: clsx(classes.root, className),\n      ownerState: ownerState,\n      ref: ref\n    }, other))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TimelineItem.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The position where the timeline's item should appear.\n   */\n  position: PropTypes.oneOf(['alternate-reverse', 'alternate', 'left', 'right']),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TimelineItem;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,CAAC;AAC3C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,WAAW;AACrE,SAASC,sBAAsB,QAAQ,oBAAoB;AAC3D,SAASC,8BAA8B,QAAQ,4BAA4B;AAC3E,OAAOC,eAAe,MAAM,6BAA6B;AACzD,SAASC,2BAA2B,QAAQ,uBAAuB;AACnE,OAAOC,8BAA8B,MAAM,4CAA4C;AACvF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,QAAQ;IACRC,OAAO;IACPC;EACF,CAAC,GAAGH,UAAU;EACd,MAAMI,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAET,8BAA8B,CAACK,QAAQ,CAAC,EAAE,CAACE,kBAAkB,IAAI,wBAAwB;EAC1G,CAAC;EACD,OAAOZ,cAAc,CAACa,KAAK,EAAET,2BAA2B,EAAEO,OAAO,CAAC;AACpE,CAAC;AACD,MAAMI,gBAAgB,GAAGlB,MAAM,CAAC,IAAI,EAAE;EACpCmB,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJX;IACF,CAAC,GAAGU,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEM,MAAM,CAACf,8BAA8B,CAACI,UAAU,CAACC,QAAQ,CAAC,CAAC,CAAC;EACnF;AACF,CAAC,CAAC,CAACW,IAAA;EAAA,IAAC;IACFZ;EACF,CAAC,GAAAY,IAAA;EAAA,OAAK9B,QAAQ,CAAC;IACb+B,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,MAAM;IACfb,QAAQ,EAAE,UAAU;IACpBc,SAAS,EAAE;EACb,CAAC,EAAEf,UAAU,CAACC,QAAQ,KAAK,MAAM,IAAI;IACnCe,aAAa,EAAE;EACjB,CAAC,EAAE,CAAChB,UAAU,CAACC,QAAQ,KAAK,WAAW,IAAID,UAAU,CAACC,QAAQ,KAAK,mBAAmB,KAAK;IACzF,kBAAAgB,MAAA,CAAkBjB,UAAU,CAACC,QAAQ,KAAK,WAAW,GAAG,MAAM,GAAG,KAAK,SAAM;MAC1Ee,aAAa,EAAE,aAAa;MAC5B,OAAAC,MAAA,CAAOzB,sBAAsB,CAACa,IAAI,IAAK;QACrCa,SAAS,EAAE;MACb,CAAC;MACD,OAAAD,MAAA,CAAOxB,8BAA8B,CAACY,IAAI,IAAK;QAC7Ca,SAAS,EAAE;MACb;IACF;EACF,CAAC,EAAE,CAAClB,UAAU,CAACG,kBAAkB,IAAI;IACnC,WAAW,EAAE;MACXgB,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE,CAAC;MACPC,OAAO,EAAE;IACX;EACF,CAAC,CAAC;AAAA,EAAC;AACH,MAAMC,YAAY,GAAG,aAAatC,KAAK,CAACuC,UAAU,CAAC,SAASD,YAAYA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACrF,MAAMf,KAAK,GAAGrB,aAAa,CAAC;IAC1BqB,KAAK,EAAEc,OAAO;IACdjB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFN,QAAQ,EAAEyB,YAAY;MACtBC;IACF,CAAC,GAAGjB,KAAK;IACTkB,KAAK,GAAG/C,6BAA6B,CAAC6B,KAAK,EAAE3B,SAAS,CAAC;EACzD,MAAM;IACJkB,QAAQ,EAAE4B;EACZ,CAAC,GAAG7C,KAAK,CAAC8C,UAAU,CAACpC,eAAe,CAAC;EACrC,IAAIS,kBAAkB,GAAG,KAAK;EAC9BnB,KAAK,CAAC+C,QAAQ,CAACC,OAAO,CAACtB,KAAK,CAACuB,QAAQ,EAAEC,KAAK,IAAI;IAC9C,IAAI/C,YAAY,CAAC+C,KAAK,EAAE,CAAC,yBAAyB,CAAC,CAAC,EAAE;MACpD/B,kBAAkB,GAAG,IAAI;IAC3B;EACF,CAAC,CAAC;EACF,MAAMH,UAAU,GAAGlB,QAAQ,CAAC,CAAC,CAAC,EAAE4B,KAAK,EAAE;IACrCT,QAAQ,EAAEyB,YAAY,IAAIG,eAAe,IAAI,OAAO;IACpD1B;EACF,CAAC,CAAC;EACF,MAAMD,OAAO,GAAGH,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMmC,YAAY,GAAGnD,KAAK,CAACoD,OAAO,CAAC,OAAO;IACxCnC,QAAQ,EAAED,UAAU,CAACC;EACvB,CAAC,CAAC,EAAE,CAACD,UAAU,CAACC,QAAQ,CAAC,CAAC;EAC1B,OAAO,aAAaH,IAAI,CAACJ,eAAe,CAAC2C,QAAQ,EAAE;IACjDC,KAAK,EAAEH,YAAY;IACnBF,QAAQ,EAAE,aAAanC,IAAI,CAACQ,gBAAgB,EAAExB,QAAQ,CAAC;MACrD6C,SAAS,EAAEzC,IAAI,CAACgB,OAAO,CAACG,IAAI,EAAEsB,SAAS,CAAC;MACxC3B,UAAU,EAAEA,UAAU;MACtByB,GAAG,EAAEA;IACP,CAAC,EAAEG,KAAK,CAAC;EACX,CAAC,CAAC;AACJ,CAAC,CAAC;AACFW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGnB,YAAY,CAACoB,SAAS,CAAC,yBAAyB;EACtF;EACA;EACA;EACA;EACA;AACF;AACA;EACET,QAAQ,EAAEhD,SAAS,CAAC0D,IAAI;EACxB;AACF;AACA;EACEzC,OAAO,EAAEjB,SAAS,CAAC2D,MAAM;EACzB;AACF;AACA;EACEjB,SAAS,EAAE1C,SAAS,CAAC4D,MAAM;EAC3B;AACF;AACA;EACE5C,QAAQ,EAAEhB,SAAS,CAAC6D,KAAK,CAAC,CAAC,mBAAmB,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;EAC9E;AACF;AACA;EACEC,EAAE,EAAE9D,SAAS,CAAC+D,SAAS,CAAC,CAAC/D,SAAS,CAACgE,OAAO,CAAChE,SAAS,CAAC+D,SAAS,CAAC,CAAC/D,SAAS,CAACiE,IAAI,EAAEjE,SAAS,CAAC2D,MAAM,EAAE3D,SAAS,CAACkE,IAAI,CAAC,CAAC,CAAC,EAAElE,SAAS,CAACiE,IAAI,EAAEjE,SAAS,CAAC2D,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAetB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}