{"ast": null, "code": "import { generateUtilityClass } from '../generateUtilityClass';\nexport function generateUtilityClasses(componentName, slots) {\n  const result = {};\n  slots.forEach(slot => {\n    result[slot] = generateUtilityClass(componentName, slot);\n  });\n  return result;\n}", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "componentName", "slots", "result", "for<PERSON>ach", "slot"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/base/generateUtilityClasses/index.js"], "sourcesContent": ["import { generateUtilityClass } from '../generateUtilityClass';\nexport function generateUtilityClasses(componentName, slots) {\n  const result = {};\n  slots.forEach(slot => {\n    result[slot] = generateUtilityClass(componentName, slot);\n  });\n  return result;\n}"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,yBAAyB;AAC9D,OAAO,SAASC,sBAAsBA,CAACC,aAAa,EAAEC,KAAK,EAAE;EAC3D,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjBD,KAAK,CAACE,OAAO,CAACC,IAAI,IAAI;IACpBF,MAAM,CAACE,IAAI,CAAC,GAAGN,oBAAoB,CAACE,aAAa,EAAEI,IAAI,CAAC;EAC1D,CAAC,CAAC;EACF,OAAOF,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}