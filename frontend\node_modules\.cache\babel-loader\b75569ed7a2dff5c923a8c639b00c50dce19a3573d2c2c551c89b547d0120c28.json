{"ast": null, "code": "'use client';\n\nexport { Badge } from './Badge';\nexport * from './Badge.types';\nexport * from './badgeClasses';", "map": {"version": 3, "names": ["Badge"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/base/Badge/index.js"], "sourcesContent": ["'use client';\n\nexport { Badge } from './Badge';\nexport * from './Badge.types';\nexport * from './badgeClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,KAAK,QAAQ,SAAS;AAC/B,cAAc,eAAe;AAC7B,cAAc,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}