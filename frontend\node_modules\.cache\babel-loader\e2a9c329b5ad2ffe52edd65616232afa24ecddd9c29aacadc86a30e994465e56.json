{"ast": null, "code": "/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The DesktopDateRangePicker component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`', '', \"You should use `import { DesktopDateRangePicker } from '@mui/x-date-pickers-pro'`\", \"or `import { DesktopDateRangePicker } from '@mui/x-date-pickers-pro/DesktopDateRangePicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The DesktopDateRangePicker component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst DesktopDateRangePicker = /*#__PURE__*/React.forwardRef(function DeprecatedDesktopDateRangePicker() {\n  warn();\n  return null;\n});\nexport default DesktopDateRangePicker;", "map": {"version": 3, "names": ["React", "warnedOnce", "warn", "console", "join", "DesktopDateRangePicker", "forwardRef", "DeprecatedDesktopDateRangePicker"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/DesktopDateRangePicker/DesktopDateRangePicker.js"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The DesktopDateRangePicker component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`', '', \"You should use `import { DesktopDateRangePicker } from '@mui/x-date-pickers-pro'`\", \"or `import { DesktopDateRangePicker } from '@mui/x-date-pickers-pro/DesktopDateRangePicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The DesktopDateRangePicker component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst DesktopDateRangePicker = /*#__PURE__*/React.forwardRef(function DeprecatedDesktopDateRangePicker() {\n  warn();\n  return null;\n});\nexport default DesktopDateRangePicker;"], "mappings": "AAAA;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,UAAU,GAAG,KAAK;AACtB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,IAAI,CAACD,UAAU,EAAE;IACfE,OAAO,CAACD,IAAI,CAAC,CAAC,kGAAkG,EAAE,EAAE,EAAE,mFAAmF,EAAE,8FAA8F,EAAE,EAAE,EAAE,qGAAqG,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;IACjaH,UAAU,GAAG,IAAI;EACnB;AACF,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMI,sBAAsB,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,SAASC,gCAAgCA,CAAA,EAAG;EACvGL,IAAI,CAAC,CAAC;EACN,OAAO,IAAI;AACb,CAAC,CAAC;AACF,eAAeG,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}