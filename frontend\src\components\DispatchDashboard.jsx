import React, { useState, useEffect } from 'react';
import { 
  Paper, Typography, Grid, Card, CardContent,
  Button, Select, MenuItem, FormControl, InputLabel
} from '@mui/material';
import { Timeline, TimelineItem, TimelineSeparator, TimelineConnector, 
  TimelineContent, TimelineDot } from '@mui/lab';
import axios from 'axios';

const DispatchDashboard = () => {
  const [deliveries, setDeliveries] = useState([]);
  const [drivers, setDrivers] = useState([]);
  const [trucks, setTrucks] = useState([]);
  const [filterDate, setFilterDate] = useState(new Date().toISOString().split('T')[0]);

  useEffect(() => {
    const fetchData = async () => {
      const deliveriesRes = await axios.get(`/api/deliveries?date=${filterDate}`);
      const driversRes = await axios.get('/api/drivers');
      const trucksRes = await axios.get('/api/trucks');
      
      setDeliveries(deliveriesRes.data);
      setDrivers(driversRes.data);
      setTrucks(trucksRes.data);
    };
    
    fetchData();
  }, [filterDate]);

  const assignDelivery = async (deliveryId, driverId, truckId) => {
    try {
      await axios.put(`/api/deliveries/${deliveryId}/assign`, {
        driver_id: driverId,
        truck_id: truckId
      });
      
      // Refresh deliveries after assignment
      const res = await axios.get(`/api/deliveries?date=${filterDate}`);
      setDeliveries(res.data);
    } catch (err) {
      console.error(err);
    }
  };

  const getStatusColor = (status) => {
    switch(status) {
      case 'Scheduled': return 'gray';
      case 'En Route': return 'blue';
      case 'Loaded': return 'orange';
      case 'In Transit': return 'purple';
      case 'Delivered': return 'green';
      default: return 'gray';
    }
  };

  return (
    <Paper style={{ padding: 20 }}>
      <Typography variant="h5" gutterBottom>Dispatch Dashboard</Typography>
      
      <Grid container spacing={3}>
        <Grid