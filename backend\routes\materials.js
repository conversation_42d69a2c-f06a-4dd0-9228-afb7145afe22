const express = require('express');
const router = express.Router();
const { Pool } = require('pg');

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://localhost:5432/construction_materials'
});

// Mock data for materials
const mockMaterials = [
  { id: 1, name: 'Crushed Concrete', category_id: 1, category_name: 'Aggregates & Base Materials', category_icon: '🪨', default_unit_measure_id: 1, base_cost_per_unit: 18.50 },
  { id: 2, name: 'Crushed Asphalt', category_id: 1, category_name: 'Aggregates & Base Materials', category_icon: '🪨', default_unit_measure_id: 1, base_cost_per_unit: 16.75 },
  { id: 3, name: 'ABC (Aggregate Base Course)', category_id: 1, category_name: 'Aggregates & Base Materials', category_icon: '🪨', default_unit_measure_id: 1, base_cost_per_unit: 22.00 },
  { id: 4, name: '#57 Stone', category_id: 1, category_name: 'Aggregates & Base Materials', category_icon: '🪨', default_unit_measure_id: 1, base_cost_per_unit: 28.00 },
  { id: 5, name: 'Fill Sand', category_id: 2, category_name: 'Sand & Soil Products', category_icon: '🏗️', default_unit_measure_id: 1, base_cost_per_unit: 18.00 },
  { id: 6, name: 'Mason Sand', category_id: 2, category_name: 'Sand & Soil Products', category_icon: '🏗️', default_unit_measure_id: 1, base_cost_per_unit: 25.00 },
  { id: 7, name: 'Concrete Sand', category_id: 2, category_name: 'Sand & Soil Products', category_icon: '🏗️', default_unit_measure_id: 1, base_cost_per_unit: 22.00 },
  { id: 8, name: 'Topsoil - Screened', category_id: 2, category_name: 'Sand & Soil Products', category_icon: '🏗️', default_unit_measure_id: 2, base_cost_per_unit: 35.00 }
];

// Get all materials with category and unit measure info
router.get('/', async (req, res) => {
  try {
    // Return mock data instead of database query
    res.json(mockMaterials);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Server error' });
  }
});

// Mock data for categories
const mockCategories = [
  { id: 1, name: 'Aggregates & Base Materials', icon: '🪨' },
  { id: 2, name: 'Sand & Soil Products', icon: '🏗️' },
  { id: 3, name: 'Recycled Materials', icon: '♻️' },
  { id: 4, name: 'Landscaping & Decorative', icon: '🌴' }
];

// Mock data for unit measures
const mockUnitMeasures = [
  { id: 1, name: 'Tons', abbreviation: 'ton', type: 'weight' },
  { id: 2, name: 'Cubic Yards', abbreviation: 'cy', type: 'volume' },
  { id: 3, name: 'Loads', abbreviation: 'load', type: 'count' },
  { id: 4, name: 'Square Feet', abbreviation: 'sqft', type: 'area' }
];

// Get material categories
router.get('/categories', async (req, res) => {
  try {
    res.json(mockCategories);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Server error' });
  }
});

// Get unit measures
router.get('/unit-measures', async (req, res) => {
  try {
    res.json(mockUnitMeasures);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Server error' });
  }
});

// Get materials by category
router.get('/by-category/:categoryId', async (req, res) => {
  try {
    const { categoryId } = req.params;
    const query = `
      SELECT m.*, um.name as default_unit_name, um.abbreviation as default_unit_abbr
      FROM materials m
      LEFT JOIN unit_measures um ON m.default_unit_measure_id = um.id
      WHERE m.category_id = $1 AND m.is_active = true
      ORDER BY m.name
    `;
    const result = await pool.query(query, [categoryId]);
    res.json(result.rows);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Server error' });
  }
});

// Get material pricing for different units
router.get('/:id/pricing', async (req, res) => {
  try {
    const { id } = req.params;
    const query = `
      SELECT mup.*, um.name as unit_name, um.abbreviation as unit_abbr
      FROM material_unit_pricing mup
      LEFT JOIN unit_measures um ON mup.unit_measure_id = um.id
      WHERE mup.material_id = $1
      ORDER BY mup.is_default DESC, um.name
    `;
    const result = await pool.query(query, [id]);
    res.json(result.rows);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Server error' });
  }
});

// Create new material
router.post('/', async (req, res) => {
  try {
    const {
      sku, name, description, category_id, default_unit_measure_id,
      base_cost_per_unit, allow_unit_override
    } = req.body;

    const query = `
      INSERT INTO materials (
        sku, name, description, category_id, default_unit_measure_id,
        base_cost_per_unit, allow_unit_override
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `;

    const result = await pool.query(query, [
      sku, name, description, category_id, default_unit_measure_id,
      base_cost_per_unit, allow_unit_override
    ]);

    res.status(201).json(result.rows[0]);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Server error' });
  }
});

module.exports = router;
