const express = require('express');
const router = express.Router();
const { Pool } = require('pg');

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://localhost:5432/construction_materials'
});

// Get all materials with category and unit measure info
router.get('/', async (req, res) => {
  try {
    const query = `
      SELECT m.*, mc.name as category_name, mc.icon as category_icon,
             um.name as default_unit_name, um.abbreviation as default_unit_abbr
      FROM materials m
      LEFT JOIN material_categories mc ON m.category_id = mc.id
      LEFT JOIN unit_measures um ON m.default_unit_measure_id = um.id
      WHERE m.is_active = true
      ORDER BY mc.name, m.name
    `;
    const result = await pool.query(query);
    res.json(result.rows);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Server error' });
  }
});

// Get material categories
router.get('/categories', async (req, res) => {
  try {
    const query = 'SELECT * FROM material_categories ORDER BY name';
    const result = await pool.query(query);
    res.json(result.rows);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Server error' });
  }
});

// Get unit measures
router.get('/unit-measures', async (req, res) => {
  try {
    const query = 'SELECT * FROM unit_measures ORDER BY type, name';
    const result = await pool.query(query);
    res.json(result.rows);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Server error' });
  }
});

// Get materials by category
router.get('/by-category/:categoryId', async (req, res) => {
  try {
    const { categoryId } = req.params;
    const query = `
      SELECT m.*, um.name as default_unit_name, um.abbreviation as default_unit_abbr
      FROM materials m
      LEFT JOIN unit_measures um ON m.default_unit_measure_id = um.id
      WHERE m.category_id = $1 AND m.is_active = true
      ORDER BY m.name
    `;
    const result = await pool.query(query, [categoryId]);
    res.json(result.rows);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Server error' });
  }
});

// Get material pricing for different units
router.get('/:id/pricing', async (req, res) => {
  try {
    const { id } = req.params;
    const query = `
      SELECT mup.*, um.name as unit_name, um.abbreviation as unit_abbr
      FROM material_unit_pricing mup
      LEFT JOIN unit_measures um ON mup.unit_measure_id = um.id
      WHERE mup.material_id = $1
      ORDER BY mup.is_default DESC, um.name
    `;
    const result = await pool.query(query, [id]);
    res.json(result.rows);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Server error' });
  }
});

// Create new material
router.post('/', async (req, res) => {
  try {
    const {
      sku, name, description, category_id, default_unit_measure_id,
      base_cost_per_unit, allow_unit_override
    } = req.body;

    const query = `
      INSERT INTO materials (
        sku, name, description, category_id, default_unit_measure_id,
        base_cost_per_unit, allow_unit_override
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `;

    const result = await pool.query(query, [
      sku, name, description, category_id, default_unit_measure_id,
      base_cost_per_unit, allow_unit_override
    ]);

    res.status(201).json(result.rows[0]);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Server error' });
  }
});

module.exports = router;
