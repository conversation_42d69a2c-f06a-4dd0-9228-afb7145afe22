{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"value\", \"defaultValue\", \"orientation\", \"direction\", \"onChange\", \"selectionFollowsFocus\", \"slotProps\", \"slots\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useSlotProps } from '../utils';\nimport { unstable_composeClasses as composeClasses } from '../composeClasses';\nimport { getTabsUtilityClass } from './tabsClasses';\nimport { useTabs } from '../useTabs';\nimport { TabsProvider } from '../useTabs/TabsProvider';\nimport { useClassNamesOverride } from '../utils/ClassNameConfigurator';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    orientation\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation]\n  };\n  return composeClasses(slots, useClassNamesOverride(getTabsUtilityClass));\n};\n\n/**\n *\n * Demos:\n *\n * - [Tabs](https://mui.com/base-ui/react-tabs/)\n *\n * API:\n *\n * - [Tabs API](https://mui.com/base-ui/react-tabs/components-api/#tabs)\n */\nconst Tabs = /*#__PURE__*/React.forwardRef(function Tabs(props, forwardedRef) {\n  var _slots$root;\n  const {\n      children,\n      orientation = 'horizontal',\n      direction = 'ltr',\n      slotProps = {},\n      slots = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    orientation,\n    direction\n  });\n  const {\n    contextValue\n  } = useTabs(ownerState);\n  const classes = useUtilityClasses(ownerState);\n  const TabsRoot = (_slots$root = slots.root) != null ? _slots$root : 'div';\n  const tabsRootProps = useSlotProps({\n    elementType: TabsRoot,\n    externalSlotProps: slotProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref: forwardedRef\n    },\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(TabsRoot, _extends({}, tabsRootProps, {\n    children: /*#__PURE__*/_jsx(TabsProvider, {\n      value: contextValue,\n      children: children\n    })\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Tabs.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The default value. Use when the component is not controlled.\n   */\n  defaultValue: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * The direction of the text.\n   * @default 'ltr'\n   */\n  direction: PropTypes.oneOf(['ltr', 'rtl']),\n  /**\n   * Callback invoked when new value is being set.\n   */\n  onChange: PropTypes.func,\n  /**\n   * The component orientation (layout flow direction).\n   * @default 'horizontal'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * If `true` the selected tab changes on focus. Otherwise it only\n   * changes on activation.\n   */\n  selectionFollowsFocus: PropTypes.bool,\n  /**\n   * The props used for each slot inside the Tabs.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Tabs.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The value of the currently selected `Tab`.\n   * If you don't want any selected `Tab`, you can set this prop to `null`.\n   */\n  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string])\n} : void 0;\nexport { Tabs };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "useSlotProps", "unstable_composeClasses", "composeClasses", "getTabsUtilityClass", "useTabs", "TabsProvider", "useClassNamesOverride", "jsx", "_jsx", "useUtilityClasses", "ownerState", "orientation", "slots", "root", "Tabs", "forwardRef", "props", "forwardedRef", "_slots$root", "children", "direction", "slotProps", "other", "contextValue", "classes", "TabsRoot", "tabsRootProps", "elementType", "externalSlotProps", "externalForwardedProps", "additionalProps", "ref", "className", "value", "process", "env", "NODE_ENV", "propTypes", "node", "string", "defaultValue", "oneOfType", "number", "oneOf", "onChange", "func", "selectionFollowsFocus", "bool", "shape", "object"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/base/Tabs/Tabs.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"value\", \"defaultValue\", \"orientation\", \"direction\", \"onChange\", \"selectionFollowsFocus\", \"slotProps\", \"slots\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useSlotProps } from '../utils';\nimport { unstable_composeClasses as composeClasses } from '../composeClasses';\nimport { getTabsUtilityClass } from './tabsClasses';\nimport { useTabs } from '../useTabs';\nimport { TabsProvider } from '../useTabs/TabsProvider';\nimport { useClassNamesOverride } from '../utils/ClassNameConfigurator';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    orientation\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation]\n  };\n  return composeClasses(slots, useClassNamesOverride(getTabsUtilityClass));\n};\n\n/**\n *\n * Demos:\n *\n * - [Tabs](https://mui.com/base-ui/react-tabs/)\n *\n * API:\n *\n * - [Tabs API](https://mui.com/base-ui/react-tabs/components-api/#tabs)\n */\nconst Tabs = /*#__PURE__*/React.forwardRef(function Tabs(props, forwardedRef) {\n  var _slots$root;\n  const {\n      children,\n      orientation = 'horizontal',\n      direction = 'ltr',\n      slotProps = {},\n      slots = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    orientation,\n    direction\n  });\n  const {\n    contextValue\n  } = useTabs(ownerState);\n  const classes = useUtilityClasses(ownerState);\n  const TabsRoot = (_slots$root = slots.root) != null ? _slots$root : 'div';\n  const tabsRootProps = useSlotProps({\n    elementType: TabsRoot,\n    externalSlotProps: slotProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref: forwardedRef\n    },\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(TabsRoot, _extends({}, tabsRootProps, {\n    children: /*#__PURE__*/_jsx(TabsProvider, {\n      value: contextValue,\n      children: children\n    })\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Tabs.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The default value. Use when the component is not controlled.\n   */\n  defaultValue: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * The direction of the text.\n   * @default 'ltr'\n   */\n  direction: PropTypes.oneOf(['ltr', 'rtl']),\n  /**\n   * Callback invoked when new value is being set.\n   */\n  onChange: PropTypes.func,\n  /**\n   * The component orientation (layout flow direction).\n   * @default 'horizontal'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * If `true` the selected tab changes on focus. Otherwise it only\n   * changes on activation.\n   */\n  selectionFollowsFocus: PropTypes.bool,\n  /**\n   * The props used for each slot inside the Tabs.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Tabs.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The value of the currently selected `Tab`.\n   * If you don't want any selected `Tab`, you can set this prop to `null`.\n   */\n  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string])\n} : void 0;\nexport { Tabs };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,cAAc,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,uBAAuB,EAAE,WAAW,EAAE,OAAO,CAAC;AAC9I,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,YAAY,QAAQ,UAAU;AACvC,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,mBAAmB;AAC7E,SAASC,mBAAmB,QAAQ,eAAe;AACnD,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,WAAW;EAC5B,CAAC;EACD,OAAOT,cAAc,CAACU,KAAK,EAAEN,qBAAqB,CAACH,mBAAmB,CAAC,CAAC;AAC1E,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMW,IAAI,GAAG,aAAahB,KAAK,CAACiB,UAAU,CAAC,SAASD,IAAIA,CAACE,KAAK,EAAEC,YAAY,EAAE;EAC5E,IAAIC,WAAW;EACf,MAAM;MACFC,QAAQ;MACRR,WAAW,GAAG,YAAY;MAC1BS,SAAS,GAAG,KAAK;MACjBC,SAAS,GAAG,CAAC,CAAC;MACdT,KAAK,GAAG,CAAC;IACX,CAAC,GAAGI,KAAK;IACTM,KAAK,GAAG1B,6BAA6B,CAACoB,KAAK,EAAEnB,SAAS,CAAC;EACzD,MAAMa,UAAU,GAAGf,QAAQ,CAAC,CAAC,CAAC,EAAEqB,KAAK,EAAE;IACrCL,WAAW;IACXS;EACF,CAAC,CAAC;EACF,MAAM;IACJG;EACF,CAAC,GAAGnB,OAAO,CAACM,UAAU,CAAC;EACvB,MAAMc,OAAO,GAAGf,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMe,QAAQ,GAAG,CAACP,WAAW,GAAGN,KAAK,CAACC,IAAI,KAAK,IAAI,GAAGK,WAAW,GAAG,KAAK;EACzE,MAAMQ,aAAa,GAAG1B,YAAY,CAAC;IACjC2B,WAAW,EAAEF,QAAQ;IACrBG,iBAAiB,EAAEP,SAAS,CAACR,IAAI;IACjCgB,sBAAsB,EAAEP,KAAK;IAC7BQ,eAAe,EAAE;MACfC,GAAG,EAAEd;IACP,CAAC;IACDP,UAAU;IACVsB,SAAS,EAAER,OAAO,CAACX;EACrB,CAAC,CAAC;EACF,OAAO,aAAaL,IAAI,CAACiB,QAAQ,EAAE9B,QAAQ,CAAC,CAAC,CAAC,EAAE+B,aAAa,EAAE;IAC7DP,QAAQ,EAAE,aAAaX,IAAI,CAACH,YAAY,EAAE;MACxC4B,KAAK,EAAEV,YAAY;MACnBJ,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFe,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGtB,IAAI,CAACuB,SAAS,CAAC,yBAAyB;EAC9E;EACA;EACA;EACA;EACA;AACF;AACA;EACElB,QAAQ,EAAEpB,SAAS,CAACuC,IAAI;EACxB;AACF;AACA;EACEN,SAAS,EAAEjC,SAAS,CAACwC,MAAM;EAC3B;AACF;AACA;EACEC,YAAY,EAAEzC,SAAS,CAAC0C,SAAS,CAAC,CAAC1C,SAAS,CAAC2C,MAAM,EAAE3C,SAAS,CAACwC,MAAM,CAAC,CAAC;EACvE;AACF;AACA;AACA;EACEnB,SAAS,EAAErB,SAAS,CAAC4C,KAAK,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;EAC1C;AACF;AACA;EACEC,QAAQ,EAAE7C,SAAS,CAAC8C,IAAI;EACxB;AACF;AACA;AACA;EACElC,WAAW,EAAEZ,SAAS,CAAC4C,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;EACxD;AACF;AACA;AACA;EACEG,qBAAqB,EAAE/C,SAAS,CAACgD,IAAI;EACrC;AACF;AACA;AACA;EACE1B,SAAS,EAAEtB,SAAS,CAACiD,KAAK,CAAC;IACzBnC,IAAI,EAAEd,SAAS,CAAC0C,SAAS,CAAC,CAAC1C,SAAS,CAAC8C,IAAI,EAAE9C,SAAS,CAACkD,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACErC,KAAK,EAAEb,SAAS,CAACiD,KAAK,CAAC;IACrBnC,IAAI,EAAEd,SAAS,CAAC4B;EAClB,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEM,KAAK,EAAElC,SAAS,CAAC0C,SAAS,CAAC,CAAC1C,SAAS,CAAC2C,MAAM,EAAE3C,SAAS,CAACwC,MAAM,CAAC;AACjE,CAAC,GAAG,KAAK,CAAC;AACV,SAASzB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}