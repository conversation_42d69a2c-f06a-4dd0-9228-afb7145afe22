{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport Typography from '@mui/material/Typography';\nimport TimelineContext from '../Timeline/TimelineContext';\nimport { getTimelineContentUtilityClass } from './timelineContentClasses';\nimport convertTimelinePositionToClass from '../internal/convertTimelinePositionToClass';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    position,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', convertTimelinePositionToClass(position)]\n  };\n  return composeClasses(slots, getTimelineContentUtilityClass, classes);\n};\nconst TimelineContentRoot = styled(Typography, {\n  name: 'MuiTimelineContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[convertTimelinePositionToClass(ownerState.position)]];\n  }\n})(_ref => {\n  let {\n    ownerState\n  } = _ref;\n  return _extends({\n    flex: 1,\n    padding: '6px 16px',\n    textAlign: 'left'\n  }, ownerState.position === 'left' && {\n    textAlign: 'right'\n  });\n});\nconst TimelineContent = /*#__PURE__*/React.forwardRef(function TimelineContent(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimelineContent'\n  });\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    position: positionContext\n  } = React.useContext(TimelineContext);\n  const ownerState = _extends({}, props, {\n    position: positionContext || 'right'\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(TimelineContentRoot, _extends({\n    component: \"div\",\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? TimelineContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TimelineContent;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "styled", "useThemeProps", "unstable_composeClasses", "composeClasses", "Typography", "TimelineContext", "getTimelineContentUtilityClass", "convertTimelinePositionToClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "position", "classes", "slots", "root", "TimelineContentRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "flex", "padding", "textAlign", "TimelineContent", "forwardRef", "inProps", "ref", "className", "other", "positionContext", "useContext", "component", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "sx", "oneOfType", "arrayOf", "func", "bool"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/TimelineContent/TimelineContent.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport Typography from '@mui/material/Typography';\nimport TimelineContext from '../Timeline/TimelineContext';\nimport { getTimelineContentUtilityClass } from './timelineContentClasses';\nimport convertTimelinePositionToClass from '../internal/convertTimelinePositionToClass';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    position,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', convertTimelinePositionToClass(position)]\n  };\n  return composeClasses(slots, getTimelineContentUtilityClass, classes);\n};\nconst TimelineContentRoot = styled(Typography, {\n  name: 'MuiTimelineContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[convertTimelinePositionToClass(ownerState.position)]];\n  }\n})(({\n  ownerState\n}) => _extends({\n  flex: 1,\n  padding: '6px 16px',\n  textAlign: 'left'\n}, ownerState.position === 'left' && {\n  textAlign: 'right'\n}));\nconst TimelineContent = /*#__PURE__*/React.forwardRef(function TimelineContent(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimelineContent'\n  });\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    position: positionContext\n  } = React.useContext(TimelineContext);\n  const ownerState = _extends({}, props, {\n    position: positionContext || 'right'\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(TimelineContentRoot, _extends({\n    component: \"div\",\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? TimelineContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TimelineContent;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,CAAC;AAC/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,WAAW;AACrE,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,eAAe,MAAM,6BAA6B;AACzD,SAASC,8BAA8B,QAAQ,0BAA0B;AACzE,OAAOC,8BAA8B,MAAM,4CAA4C;AACvF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,QAAQ;IACRC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAER,8BAA8B,CAACK,QAAQ,CAAC;EACzD,CAAC;EACD,OAAOT,cAAc,CAACW,KAAK,EAAER,8BAA8B,EAAEO,OAAO,CAAC;AACvE,CAAC;AACD,MAAMG,mBAAmB,GAAGhB,MAAM,CAACI,UAAU,EAAE;EAC7Ca,IAAI,EAAE,oBAAoB;EAC1BC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJV;IACF,CAAC,GAAGS,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEM,MAAM,CAACd,8BAA8B,CAACI,UAAU,CAACC,QAAQ,CAAC,CAAC,CAAC;EACnF;AACF,CAAC,CAAC,CAACU,IAAA;EAAA,IAAC;IACFX;EACF,CAAC,GAAAW,IAAA;EAAA,OAAK3B,QAAQ,CAAC;IACb4B,IAAI,EAAE,CAAC;IACPC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE;EACb,CAAC,EAAEd,UAAU,CAACC,QAAQ,KAAK,MAAM,IAAI;IACnCa,SAAS,EAAE;EACb,CAAC,CAAC;AAAA,EAAC;AACH,MAAMC,eAAe,GAAG,aAAa7B,KAAK,CAAC8B,UAAU,CAAC,SAASD,eAAeA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC3F,MAAMT,KAAK,GAAGnB,aAAa,CAAC;IAC1BmB,KAAK,EAAEQ,OAAO;IACdX,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFa;IACF,CAAC,GAAGV,KAAK;IACTW,KAAK,GAAGrC,6BAA6B,CAAC0B,KAAK,EAAExB,SAAS,CAAC;EACzD,MAAM;IACJgB,QAAQ,EAAEoB;EACZ,CAAC,GAAGnC,KAAK,CAACoC,UAAU,CAAC5B,eAAe,CAAC;EACrC,MAAMM,UAAU,GAAGhB,QAAQ,CAAC,CAAC,CAAC,EAAEyB,KAAK,EAAE;IACrCR,QAAQ,EAAEoB,eAAe,IAAI;EAC/B,CAAC,CAAC;EACF,MAAMnB,OAAO,GAAGH,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACO,mBAAmB,EAAErB,QAAQ,CAAC;IACrDuC,SAAS,EAAE,KAAK;IAChBJ,SAAS,EAAE/B,IAAI,CAACc,OAAO,CAACE,IAAI,EAAEe,SAAS,CAAC;IACxCnB,UAAU,EAAEA,UAAU;IACtBkB,GAAG,EAAEA;EACP,CAAC,EAAEE,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACFI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGX,eAAe,CAACY,SAAS,CAAC,yBAAyB;EACzF;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEzC,SAAS,CAAC0C,IAAI;EACxB;AACF;AACA;EACE3B,OAAO,EAAEf,SAAS,CAAC2C,MAAM;EACzB;AACF;AACA;EACEX,SAAS,EAAEhC,SAAS,CAAC4C,MAAM;EAC3B;AACF;AACA;EACEC,EAAE,EAAE7C,SAAS,CAAC8C,SAAS,CAAC,CAAC9C,SAAS,CAAC+C,OAAO,CAAC/C,SAAS,CAAC8C,SAAS,CAAC,CAAC9C,SAAS,CAACgD,IAAI,EAAEhD,SAAS,CAAC2C,MAAM,EAAE3C,SAAS,CAACiD,IAAI,CAAC,CAAC,CAAC,EAAEjD,SAAS,CAACgD,IAAI,EAAEhD,SAAS,CAAC2C,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAef,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}