const express = require('express');
const router = express.Router();
const { Pool } = require('pg');

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://localhost:5432/construction_materials'
});

// Mock data for clients
const mockClients = [
  { id: 1, company_name: 'Boynton Beach Construction LLC', contact_name: '<PERSON>', email: '<EMAIL>', phone: '************' },
  { id: 2, company_name: 'Palm Beach Builders', contact_name: '<PERSON>', email: '<EMAIL>', phone: '************' },
  { id: 3, company_name: 'Coastal Development Group', contact_name: '<PERSON>', email: '<EMAIL>', phone: '************' },
  { id: 4, company_name: 'Renaissance Commons Contractors', contact_name: '<PERSON>', email: '<EMAIL>', phone: '************' },
  { id: 5, company_name: 'South Florida Infrastructure', contact_name: '<PERSON>', email: '<EMAIL>', phone: '************' }
];

// Get all clients
router.get('/', async (req, res) => {
  try {
    res.json(mockClients);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Server error' });
  }
});

module.exports = router;
