{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Alert from '@mui/material/Alert';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\n\n/**\n * @ignore - do not document.\n */\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedAlert(props, ref) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The Alert component was moved from the lab to the core.', '', \"You should use `import { Alert } from '@mui/material'`\", \"or `import Alert from '@mui/material/Alert'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return /*#__PURE__*/_jsx(Alert, _extends({\n    ref: ref\n  }, props));\n});", "map": {"version": 3, "names": ["_extends", "React", "<PERSON><PERSON>", "jsx", "_jsx", "warnedOnce", "forwardRef", "Depre<PERSON><PERSON><PERSON><PERSON>", "props", "ref", "console", "warn", "join"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/Alert/Alert.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Alert from '@mui/material/Alert';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\n\n/**\n * @ignore - do not document.\n */\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedAlert(props, ref) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The Alert component was moved from the lab to the core.', '', \"You should use `import { Alert } from '@mui/material'`\", \"or `import Alert from '@mui/material/Alert'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return /*#__PURE__*/_jsx(Alert, _extends({\n    ref: ref\n  }, props));\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,KAAK,MAAM,qBAAqB;AACvC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,IAAIC,UAAU,GAAG,KAAK;;AAEtB;AACA;AACA;AACA,eAAe,aAAaJ,KAAK,CAACK,UAAU,CAAC,SAASC,eAAeA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAChF,IAAI,CAACJ,UAAU,EAAE;IACfK,OAAO,CAACC,IAAI,CAAC,CAAC,8DAA8D,EAAE,EAAE,EAAE,wDAAwD,EAAE,8CAA8C,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvMP,UAAU,GAAG,IAAI;EACnB;EACA,OAAO,aAAaD,IAAI,CAACF,KAAK,EAAEF,QAAQ,CAAC;IACvCS,GAAG,EAAEA;EACP,CAAC,EAAED,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}