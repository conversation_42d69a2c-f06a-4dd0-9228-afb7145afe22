{"ast": null, "code": "import React from'react';import{BrowserRouter as Router,Routes,Route,Navigate}from'react-router-dom';import{ThemeProvider}from'@mui/material/styles';import CssBaseline from'@mui/material/CssBaseline';// Theme and Context\nimport modernTheme from'./theme/modernTheme';import{LanguageProvider}from'./contexts/LanguageContext';// Auth Components\nimport ModernLogin from'./components/auth/ModernLogin';// Admin Components\nimport DispatchDashboard from'./components/DispatchDashboard';import PurchaseOrderForm from'./components/PurchaseOrderForm';import GeofenceManager from'./components/GeofenceManager';// Driver Components\nimport DriverDashboard from'./components/driver/DriverDashboard';import DeliveryDetails from'./components/driver/DeliveryDetails';// Layout Components\nimport AdminLayout from'./components/layout/AdminLayout';import DriverLayout from'./components/driver/DriverLayout';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(LanguageProvider,{children:/*#__PURE__*/_jsxs(ThemeProvider,{theme:modernTheme,children:[/*#__PURE__*/_jsx(CssBaseline,{}),/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(ModernLogin,{})}),/*#__PURE__*/_jsxs(Route,{path:\"/driver\",element:/*#__PURE__*/_jsx(DriverLayout,{}),children:[/*#__PURE__*/_jsx(Route,{index:true,element:/*#__PURE__*/_jsx(Navigate,{to:\"/driver/dashboard\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"dashboard\",element:/*#__PURE__*/_jsx(DriverDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"delivery/:id\",element:/*#__PURE__*/_jsx(DeliveryDetails,{})})]}),/*#__PURE__*/_jsxs(Route,{path:\"/trucker\",element:/*#__PURE__*/_jsx(DriverLayout,{}),children:[/*#__PURE__*/_jsx(Route,{index:true,element:/*#__PURE__*/_jsx(Navigate,{to:\"/trucker/dashboard\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"dashboard\",element:/*#__PURE__*/_jsx(DriverDashboard,{})})]}),/*#__PURE__*/_jsxs(Route,{path:\"/\",element:/*#__PURE__*/_jsx(AdminLayout,{}),children:[/*#__PURE__*/_jsx(Route,{index:true,element:/*#__PURE__*/_jsx(Navigate,{to:\"/dispatch\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"dispatch\",element:/*#__PURE__*/_jsx(DispatchDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"purchase-orders\",element:/*#__PURE__*/_jsx(PurchaseOrderForm,{})}),/*#__PURE__*/_jsx(Route,{path:\"geofences\",element:/*#__PURE__*/_jsx(GeofenceManager,{})})]}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true})})]})})]})});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "ThemeProvider", "CssBaseline", "modernTheme", "LanguageProvider", "ModernLogin", "DispatchDashboard", "PurchaseOrderForm", "GeofenceManager", "DriverDashboard", "DeliveryDetails", "AdminLayout", "DriverLayout", "jsx", "_jsx", "jsxs", "_jsxs", "App", "children", "theme", "path", "element", "index", "to", "replace"], "sources": ["C:/NewSiteKevin/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { ThemeProvider } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\n\n// Theme and Context\nimport modernTheme from './theme/modernTheme';\nimport { LanguageProvider } from './contexts/LanguageContext';\n\n// Auth Components\nimport ModernLogin from './components/auth/ModernLogin';\n\n// Admin Components\nimport DispatchDashboard from './components/DispatchDashboard';\nimport PurchaseOrderForm from './components/PurchaseOrderForm';\nimport GeofenceManager from './components/GeofenceManager';\n\n// Driver Components\nimport DriverDashboard from './components/driver/DriverDashboard';\nimport DeliveryDetails from './components/driver/DeliveryDetails';\n\n// Layout Components\nimport AdminLayout from './components/layout/AdminLayout';\nimport DriverLayout from './components/driver/DriverLayout';\n\nfunction App() {\n  return (\n    <LanguageProvider>\n      <ThemeProvider theme={modernTheme}>\n        <CssBaseline />\n        <Router>\n          <Routes>\n            {/* Auth Routes */}\n            <Route path=\"/login\" element={<ModernLogin />} />\n\n            {/* Driver Routes */}\n            <Route path=\"/driver\" element={<DriverLayout />}>\n              <Route index element={<Navigate to=\"/driver/dashboard\" replace />} />\n              <Route path=\"dashboard\" element={<DriverDashboard />} />\n              <Route path=\"delivery/:id\" element={<DeliveryDetails />} />\n            </Route>\n\n            {/* Trucker Routes */}\n            <Route path=\"/trucker\" element={<DriverLayout />}>\n              <Route index element={<Navigate to=\"/trucker/dashboard\" replace />} />\n              <Route path=\"dashboard\" element={<DriverDashboard />} />\n            </Route>\n\n            {/* Admin Routes */}\n            <Route path=\"/\" element={<AdminLayout />}>\n              <Route index element={<Navigate to=\"/dispatch\" replace />} />\n              <Route path=\"dispatch\" element={<DispatchDashboard />} />\n              <Route path=\"purchase-orders\" element={<PurchaseOrderForm />} />\n              <Route path=\"geofences\" element={<GeofenceManager />} />\n            </Route>\n\n            {/* Redirect to login for unknown routes */}\n            <Route path=\"*\" element={<Navigate to=\"/login\" replace />} />\n          </Routes>\n        </Router>\n      </ThemeProvider>\n    </LanguageProvider>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,KAAQ,kBAAkB,CACnF,OAASC,aAAa,KAAQ,sBAAsB,CACpD,MAAO,CAAAC,WAAW,KAAM,2BAA2B,CAEnD;AACA,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,OAASC,gBAAgB,KAAQ,4BAA4B,CAE7D;AACA,MAAO,CAAAC,WAAW,KAAM,+BAA+B,CAEvD;AACA,MAAO,CAAAC,iBAAiB,KAAM,gCAAgC,CAC9D,MAAO,CAAAC,iBAAiB,KAAM,gCAAgC,CAC9D,MAAO,CAAAC,eAAe,KAAM,8BAA8B,CAE1D;AACA,MAAO,CAAAC,eAAe,KAAM,qCAAqC,CACjE,MAAO,CAAAC,eAAe,KAAM,qCAAqC,CAEjE;AACA,MAAO,CAAAC,WAAW,KAAM,iCAAiC,CACzD,MAAO,CAAAC,YAAY,KAAM,kCAAkC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5D,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACEH,IAAA,CAACV,gBAAgB,EAAAc,QAAA,cACfF,KAAA,CAACf,aAAa,EAACkB,KAAK,CAAEhB,WAAY,CAAAe,QAAA,eAChCJ,IAAA,CAACZ,WAAW,GAAE,CAAC,cACfY,IAAA,CAACjB,MAAM,EAAAqB,QAAA,cACLF,KAAA,CAAClB,MAAM,EAAAoB,QAAA,eAELJ,IAAA,CAACf,KAAK,EAACqB,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEP,IAAA,CAACT,WAAW,GAAE,CAAE,CAAE,CAAC,cAGjDW,KAAA,CAACjB,KAAK,EAACqB,IAAI,CAAC,SAAS,CAACC,OAAO,cAAEP,IAAA,CAACF,YAAY,GAAE,CAAE,CAAAM,QAAA,eAC9CJ,IAAA,CAACf,KAAK,EAACuB,KAAK,MAACD,OAAO,cAAEP,IAAA,CAACd,QAAQ,EAACuB,EAAE,CAAC,mBAAmB,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,cACrEV,IAAA,CAACf,KAAK,EAACqB,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEP,IAAA,CAACL,eAAe,GAAE,CAAE,CAAE,CAAC,cACxDK,IAAA,CAACf,KAAK,EAACqB,IAAI,CAAC,cAAc,CAACC,OAAO,cAAEP,IAAA,CAACJ,eAAe,GAAE,CAAE,CAAE,CAAC,EACtD,CAAC,cAGRM,KAAA,CAACjB,KAAK,EAACqB,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEP,IAAA,CAACF,YAAY,GAAE,CAAE,CAAAM,QAAA,eAC/CJ,IAAA,CAACf,KAAK,EAACuB,KAAK,MAACD,OAAO,cAAEP,IAAA,CAACd,QAAQ,EAACuB,EAAE,CAAC,oBAAoB,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,cACtEV,IAAA,CAACf,KAAK,EAACqB,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEP,IAAA,CAACL,eAAe,GAAE,CAAE,CAAE,CAAC,EACnD,CAAC,cAGRO,KAAA,CAACjB,KAAK,EAACqB,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEP,IAAA,CAACH,WAAW,GAAE,CAAE,CAAAO,QAAA,eACvCJ,IAAA,CAACf,KAAK,EAACuB,KAAK,MAACD,OAAO,cAAEP,IAAA,CAACd,QAAQ,EAACuB,EAAE,CAAC,WAAW,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,cAC7DV,IAAA,CAACf,KAAK,EAACqB,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEP,IAAA,CAACR,iBAAiB,GAAE,CAAE,CAAE,CAAC,cACzDQ,IAAA,CAACf,KAAK,EAACqB,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEP,IAAA,CAACP,iBAAiB,GAAE,CAAE,CAAE,CAAC,cAChEO,IAAA,CAACf,KAAK,EAACqB,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEP,IAAA,CAACN,eAAe,GAAE,CAAE,CAAE,CAAC,EACnD,CAAC,cAGRM,IAAA,CAACf,KAAK,EAACqB,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEP,IAAA,CAACd,QAAQ,EAACuB,EAAE,CAAC,QAAQ,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,EACvD,CAAC,CACH,CAAC,EACI,CAAC,CACA,CAAC,CAEvB,CAEA,cAAe,CAAAP,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}