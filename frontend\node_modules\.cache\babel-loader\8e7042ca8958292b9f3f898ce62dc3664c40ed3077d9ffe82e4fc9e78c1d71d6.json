{"ast": null, "code": "'use client';\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The DesktopTimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { DesktopTimePicker } from '@mui/x-date-pickers'`\", \"or `import { DesktopTimePicker } from '@mui/x-date-pickers/DesktopTimePicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The DesktopTimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst DesktopTimePicker = /*#__PURE__*/React.forwardRef(function DeprecatedDesktopTimePicker() {\n  warn();\n  return null;\n});\nexport default DesktopTimePicker;", "map": {"version": 3, "names": ["React", "warnedOnce", "warn", "console", "join", "DesktopTimePicker", "forwardRef", "DeprecatedDesktopTimePicker"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/DesktopTimePicker/DesktopTimePicker.js"], "sourcesContent": ["'use client';\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The DesktopTimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { DesktopTimePicker } from '@mui/x-date-pickers'`\", \"or `import { DesktopTimePicker } from '@mui/x-date-pickers/DesktopTimePicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The DesktopTimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst DesktopTimePicker = /*#__PURE__*/React.forwardRef(function DeprecatedDesktopTimePicker() {\n  warn();\n  return null;\n});\nexport default DesktopTimePicker;"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,UAAU,GAAG,KAAK;AACtB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,IAAI,CAACD,UAAU,EAAE;IACfE,OAAO,CAACD,IAAI,CAAC,CAAC,0FAA0F,EAAE,EAAE,EAAE,0EAA0E,EAAE,gFAAgF,EAAE,EAAE,EAAE,qGAAqG,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;IAClYH,UAAU,GAAG,IAAI;EACnB;AACF,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMI,iBAAiB,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,SAASC,2BAA2BA,CAAA,EAAG;EAC7FL,IAAI,CAAC,CAAC;EACN,OAAO,IAAI;AACb,CAAC,CAAC;AACF,eAAeG,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}