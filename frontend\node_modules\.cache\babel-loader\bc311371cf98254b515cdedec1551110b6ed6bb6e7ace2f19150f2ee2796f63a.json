{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport ToggleButtonGroup from '@mui/material/ToggleButtonGroup';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\n\n/**\n * @ignore - do not document.\n */\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedToggleButtonGroup(props, ref) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The ToggleButtonGroup component was moved from the lab to the core.', '', \"You should use `import { ToggleButtonGroup } from '@mui/material'`\", \"or `import ToggleButtonGroup from '@mui/material/ToggleButtonGroup'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return /*#__PURE__*/_jsx(ToggleButtonGroup, _extends({\n    ref: ref\n  }, props));\n});", "map": {"version": 3, "names": ["_extends", "React", "ToggleButtonGroup", "jsx", "_jsx", "warnedOnce", "forwardRef", "DeprecatedToggleButtonGroup", "props", "ref", "console", "warn", "join"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/ToggleButtonGroup/ToggleButtonGroup.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport ToggleButtonGroup from '@mui/material/ToggleButtonGroup';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\n\n/**\n * @ignore - do not document.\n */\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedToggleButtonGroup(props, ref) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The ToggleButtonGroup component was moved from the lab to the core.', '', \"You should use `import { ToggleButtonGroup } from '@mui/material'`\", \"or `import ToggleButtonGroup from '@mui/material/ToggleButtonGroup'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return /*#__PURE__*/_jsx(ToggleButtonGroup, _extends({\n    ref: ref\n  }, props));\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,IAAIC,UAAU,GAAG,KAAK;;AAEtB;AACA;AACA;AACA,eAAe,aAAaJ,KAAK,CAACK,UAAU,CAAC,SAASC,2BAA2BA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC5F,IAAI,CAACJ,UAAU,EAAE;IACfK,OAAO,CAACC,IAAI,CAAC,CAAC,0EAA0E,EAAE,EAAE,EAAE,oEAAoE,EAAE,sEAAsE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvPP,UAAU,GAAG,IAAI;EACnB;EACA,OAAO,aAAaD,IAAI,CAACF,iBAAiB,EAAEF,QAAQ,CAAC;IACnDS,GAAG,EAAEA;EACP,CAAC,EAAED,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}