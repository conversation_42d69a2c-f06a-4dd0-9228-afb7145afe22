const express = require('express');
const router = express.Router();
const { Pool } = require('pg');

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://localhost:5432/construction_materials'
});

// Get all purchase orders
router.get('/', async (req, res) => {
  try {
    const query = `
      SELECT po.*, c.company_name as customer_name,
             COUNT(pli.id) as line_item_count,
             SUM(pli.line_total) as total_amount
      FROM purchase_orders po
      LEFT JOIN customers c ON po.customer_id = c.id
      LEFT JOIN po_line_items pli ON po.id = pli.po_id
      GROUP BY po.id, c.company_name
      ORDER BY po.created_at DESC
    `;
    
    const result = await pool.query(query);
    res.json(result.rows);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Server error' });
  }
});

// Get purchase order by ID with line items
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Get purchase order
    const poQuery = `
      SELECT po.*, c.company_name as customer_name
      FROM purchase_orders po
      LEFT JOIN customers c ON po.customer_id = c.id
      WHERE po.id = $1
    `;
    const poResult = await pool.query(poQuery, [id]);
    
    if (poResult.rows.length === 0) {
      return res.status(404).json({ error: 'Purchase order not found' });
    }
    
    // Get line items
    const lineItemsQuery = `
      SELECT pli.*, m.name as material_name, m.sku as material_sku,
             um.name as unit_measure_name, um.abbreviation as unit_abbreviation
      FROM po_line_items pli
      LEFT JOIN materials m ON pli.material_id = m.id
      LEFT JOIN unit_measures um ON pli.unit_measure_id = um.id
      WHERE pli.po_id = $1
      ORDER BY pli.id
    `;
    const lineItemsResult = await pool.query(lineItemsQuery, [id]);
    
    const purchaseOrder = {
      ...poResult.rows[0],
      line_items: lineItemsResult.rows
    };
    
    res.json(purchaseOrder);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Server error' });
  }
});

// Create new purchase order
router.post('/', async (req, res) => {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    const {
      customer_id,
      pickup_location,
      pickup_latitude,
      pickup_longitude,
      notes,
      line_items,
      created_by = '<EMAIL>'
    } = req.body;
    
    // Create purchase order
    const poQuery = `
      INSERT INTO purchase_orders (
        customer_id, pickup_location, pickup_latitude, pickup_longitude, 
        notes, created_by
      ) VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `;
    
    const poResult = await client.query(poQuery, [
      customer_id, pickup_location, pickup_latitude, pickup_longitude,
      notes, created_by
    ]);
    
    const purchaseOrder = poResult.rows[0];
    
    // Create line items
    const lineItemPromises = line_items.map(async (item) => {
      const lineItemQuery = `
        INSERT INTO po_line_items (
          po_id, material_id, unit_measure_id, quantity, unit_price,
          hauling_rate, dropoff_location, dropoff_latitude, dropoff_longitude,
          special_instructions
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING *
      `;
      
      return client.query(lineItemQuery, [
        purchaseOrder.id,
        item.material_id,
        item.unit_measure_id,
        item.quantity,
        item.unit_price,
        item.hauling_rate,
        item.dropoff_location,
        item.dropoff_latitude,
        item.dropoff_longitude,
        item.special_instructions
      ]);
    });
    
    await Promise.all(lineItemPromises);
    
    // Calculate and update totals
    const totalQuery = `
      UPDATE purchase_orders 
      SET subtotal = (
        SELECT SUM(line_total) FROM po_line_items WHERE po_id = $1
      ),
      total = (
        SELECT SUM(line_total) FROM po_line_items WHERE po_id = $1
      )
      WHERE id = $1
      RETURNING *
    `;
    
    const updatedPO = await client.query(totalQuery, [purchaseOrder.id]);
    
    await client.query('COMMIT');
    
    res.status(201).json(updatedPO.rows[0]);
  } catch (err) {
    await client.query('ROLLBACK');
    console.error(err);
    res.status(500).json({ error: 'Failed to create purchase order' });
  } finally {
    client.release();
  }
});

// Update purchase order status
router.put('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    const query = `
      UPDATE purchase_orders 
      SET status = $1, updated_at = CURRENT_TIMESTAMP
      WHERE id = $2
      RETURNING *
    `;
    
    const result = await pool.query(query, [status, id]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Purchase order not found' });
    }
    
    res.json(result.rows[0]);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Server error' });
  }
});

// Create deliveries from purchase order line items
router.post('/:id/create-deliveries', async (req, res) => {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    const { id } = req.params;
    const { line_item_ids, scheduled_date, preferred_time_window } = req.body;
    
    // Get purchase order and line items
    const poQuery = 'SELECT * FROM purchase_orders WHERE id = $1';
    const poResult = await client.query(poQuery, [id]);
    
    if (poResult.rows.length === 0) {
      return res.status(404).json({ error: 'Purchase order not found' });
    }
    
    const purchaseOrder = poResult.rows[0];
    
    // Get selected line items
    const lineItemsQuery = `
      SELECT pli.*, m.name as material_name, um.abbreviation as unit_abbreviation
      FROM po_line_items pli
      LEFT JOIN materials m ON pli.material_id = m.id
      LEFT JOIN unit_measures um ON pli.unit_measure_id = um.id
      WHERE pli.po_id = $1 AND pli.id = ANY($2)
    `;
    
    const lineItemsResult = await client.query(lineItemsQuery, [id, line_item_ids]);
    
    // Create deliveries
    const deliveryPromises = lineItemsResult.rows.map(async (lineItem) => {
      const deliveryQuery = `
        INSERT INTO deliveries (
          po_line_item_id, po_id, pickup_location, dropoff_location,
          pickup_latitude, pickup_longitude, dropoff_latitude, dropoff_longitude,
          scheduled_date, preferred_time_window, material_name, quantity,
          unit_measure, hauling_rate, special_instructions
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
        RETURNING *
      `;
      
      return client.query(deliveryQuery, [
        lineItem.id,
        id,
        purchaseOrder.pickup_location,
        lineItem.dropoff_location,
        purchaseOrder.pickup_latitude,
        purchaseOrder.pickup_longitude,
        lineItem.dropoff_latitude,
        lineItem.dropoff_longitude,
        scheduled_date,
        preferred_time_window,
        lineItem.material_name,
        lineItem.quantity,
        lineItem.unit_abbreviation,
        lineItem.hauling_rate,
        lineItem.special_instructions
      ]);
    });
    
    const deliveryResults = await Promise.all(deliveryPromises);
    
    // Update line item status
    const updateStatusQuery = `
      UPDATE po_line_items 
      SET status = 'Scheduled'
      WHERE id = ANY($1)
    `;
    
    await client.query(updateStatusQuery, [line_item_ids]);
    
    await client.query('COMMIT');
    
    const deliveries = deliveryResults.map(result => result.rows[0]);
    res.status(201).json(deliveries);
    
  } catch (err) {
    await client.query('ROLLBACK');
    console.error(err);
    res.status(500).json({ error: 'Failed to create deliveries' });
  } finally {
    client.release();
  }
});

// Get purchase order analytics
router.get('/analytics/summary', async (req, res) => {
  try {
    const query = `
      SELECT 
        COUNT(*) as total_pos,
        COUNT(CASE WHEN status = 'Active' THEN 1 END) as active_pos,
        COUNT(CASE WHEN status = 'Draft' THEN 1 END) as draft_pos,
        COUNT(CASE WHEN status = 'Completed' THEN 1 END) as completed_pos,
        SUM(total) as total_value,
        AVG(total) as avg_value
      FROM purchase_orders
      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
    `;
    
    const result = await pool.query(query);
    res.json(result.rows[0]);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Server error' });
  }
});

module.exports = router;
