{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Rating from '@mui/material/Rating';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\n\n/**\n * @ignore - do not document.\n */\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedRating(props, ref) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The Rating component was moved from the lab to the core.', '', \"You should use `import { Rating } from '@mui/material'`\", \"or `import Rating from '@mui/material/Rating'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return /*#__PURE__*/_jsx(Rating, _extends({\n    ref: ref\n  }, props));\n});", "map": {"version": 3, "names": ["_extends", "React", "Rating", "jsx", "_jsx", "warnedOnce", "forwardRef", "DeprecatedRating", "props", "ref", "console", "warn", "join"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/Rating/Rating.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Rating from '@mui/material/Rating';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\n\n/**\n * @ignore - do not document.\n */\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedRating(props, ref) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The Rating component was moved from the lab to the core.', '', \"You should use `import { Rating } from '@mui/material'`\", \"or `import Rating from '@mui/material/Rating'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return /*#__PURE__*/_jsx(Rating, _extends({\n    ref: ref\n  }, props));\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,IAAIC,UAAU,GAAG,KAAK;;AAEtB;AACA;AACA;AACA,eAAe,aAAaJ,KAAK,CAACK,UAAU,CAAC,SAASC,gBAAgBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjF,IAAI,CAACJ,UAAU,EAAE;IACfK,OAAO,CAACC,IAAI,CAAC,CAAC,+DAA+D,EAAE,EAAE,EAAE,yDAAyD,EAAE,gDAAgD,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3MP,UAAU,GAAG,IAAI;EACnB;EACA,OAAO,aAAaD,IAAI,CAACF,MAAM,EAAEF,QAAQ,CAAC;IACxCS,GAAG,EAAEA;EACP,CAAC,EAAED,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}