{"ast": null, "code": "'use client';\n\nexport { useSwitch } from './useSwitch';\nexport * from './useSwitch.types';", "map": {"version": 3, "names": ["useSwitch"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/base/useSwitch/index.js"], "sourcesContent": ["'use client';\n\nexport { useSwitch } from './useSwitch';\nexport * from './useSwitch.types';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,SAAS,QAAQ,aAAa;AACvC,cAAc,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}