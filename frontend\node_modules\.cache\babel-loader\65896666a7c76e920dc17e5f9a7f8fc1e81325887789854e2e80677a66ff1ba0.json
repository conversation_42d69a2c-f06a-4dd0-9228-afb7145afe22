{"ast": null, "code": "'use client';\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The TimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { TimePicker } from '@mui/x-date-pickers'`\", \"or `import { TimePicker } from '@mui/x-date-pickers/TimePicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The StaticTimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst TimePicker = /*#__PURE__*/React.forwardRef(function DeprecatedTimePicker() {\n  warn();\n  return null;\n});\nexport default TimePicker;", "map": {"version": 3, "names": ["React", "warnedOnce", "warn", "console", "join", "TimePicker", "forwardRef", "DeprecatedTimePicker"], "sources": ["C:/NewSiteKevin/frontend/node_modules/@mui/lab/TimePicker/TimePicker.js"], "sourcesContent": ["'use client';\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The TimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { TimePicker } from '@mui/x-date-pickers'`\", \"or `import { TimePicker } from '@mui/x-date-pickers/TimePicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The StaticTimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst TimePicker = /*#__PURE__*/React.forwardRef(function DeprecatedTimePicker() {\n  warn();\n  return null;\n});\nexport default TimePicker;"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,UAAU,GAAG,KAAK;AACtB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,IAAI,CAACD,UAAU,EAAE;IACfE,OAAO,CAACD,IAAI,CAAC,CAAC,mFAAmF,EAAE,EAAE,EAAE,mEAAmE,EAAE,kEAAkE,EAAE,EAAE,EAAE,qGAAqG,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;IACtWH,UAAU,GAAG,IAAI;EACnB;AACF,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMI,UAAU,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,SAASC,oBAAoBA,CAAA,EAAG;EAC/EL,IAAI,CAAC,CAAC;EACN,OAAO,IAAI;AACb,CAAC,CAAC;AACF,eAAeG,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}